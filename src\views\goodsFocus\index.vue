<script setup>
// http://sytdemo-sq.sapi.viwor.net/opsLogin?userName=80100000&opsToken=97df047569b857095a4fea5754126bc1
import defaultImg from '@/assets/images/goodsFocus/default.png'
import { h, ref, reactive, onBeforeMount, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
import { Search } from '@element-plus/icons-vue'
import { sortGroup } from '@/api/goodsFocus'
import { showToast, showConfirmDialog } from 'vant'
import draggable from "vuedraggable";

import { selectUserGoodsGroupList, selectGoodFocusIndex, addGroup, updateGroup, deleteGroup, sortGroupGoods, deleteGroupGoods, shareGroup } from '@/api/goodsFocus'
// import { fa } from 'element-plus/es/locale'
import wx from 'weixin-webview-jssdk'
import { api } from '@/api/ai'

const showBottom = ref(false)

// 获取当前日期区间(yy-mm-dd - yy-mm-dd)
const getCurrentDate = () => {
  const date = new Date()
  const dateStr = `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`
  return `${dateStr} - ${dateStr}`
}

// 获取昨天日期区间(yy-mm-dd - yy-mm-dd)
const getYesterdayDate = () => {
  const date = new Date()
  date.setDate(date.getDate() - 1)
  const dateStr = `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`
  return `${dateStr} - ${dateStr}`
}

// 获取本周日期区间(周一 - 周日)(yy-mm-dd - yy-mm-dd)
const getWeekStartDate = () => {
  const date = new Date()
  const day = date.getDay()
  const diff = date.getDate() - day + (day === 0 ? -6 : 1)
  date.setDate(diff)
  const startDate = `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`

  const endDate = new Date(date)
  endDate.setDate(date.getDate() + 6)
  const endDateStr = `${endDate.getFullYear()}-${padZero(endDate.getMonth() + 1)}-${padZero(endDate.getDate())}`

  return `${startDate} - ${endDateStr}`
}

// 获取本月日期区间(1号 - 月末)(yy-mm-dd - yy-mm-dd)
const getMonthStartDate = () => {
  const date = new Date()
  const startDate = `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-01`

  const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate()
  const endDate = `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(lastDay)}`

  return `${startDate} - ${endDate}`
}

// 补零函数
const padZero = (num) => {
  return num < 10 ? `0${num}` : num
}

const currentTimeIndex = ref(0)

const timeList = reactive([
  {
    value: getCurrentDate(), // 实时
    label: '实时',
  },
  {
    value: getYesterdayDate(), // 昨天
    label: '昨天',
  },
  {
    value: getWeekStartDate(), // 本周
    label: '本周',
  },
  {
    value: getMonthStartDate(), // 本月
    label: '本月',
  },
  {
    value: 'customize',
    label: '自定义',
  },
])

const timeChange = (item, index) => {
  currentTimeIndex.value = index
  if (currentTimeIndex.value !== 4) {
    currentDate.value.length = 0
    timeValue.value = timeList[currentTimeIndex.value].value
    startTime.value = timeValue.value.split(' - ')[0]
    endTime.value = timeValue.value.split(' - ')[1]
  } else {
    startTime.value = ''
    endTime.value = ''
  }
  if (item === 'startTime') {
    showDateBottom.value = true
    showDateTyepe.value = item
  } else if (item === 'endTime') {
    showDateBottom.value = true
    showDateTyepe.value = item
  }
}

const startTime = ref('')

const endTime = ref('')

const showDateBottom = ref(false)

const showDateTyepe = ref('')

const currentDate = ref([])

const currentGroupId = ref('')

const scanCodeResult = ref('')

const columnsType = ['year', 'month', 'day']
const formatter = (type, option) => {
  if (type === 'year') {
    option.text += '年'
  }
  if (type === 'month') {
    option.text += '月'
  }
  if (type === 'day') {
    option.text += '日'
  }
  return option
}

const confirmDate = () => {
  if (showDateTyepe.value === 'startTime') {
    startTime.value = currentDate.value.join('-')
  }
  if (showDateTyepe.value === 'endTime') {
    endTime.value = currentDate.value.join('-')
  }

  // 新增校验逻辑
  if (startTime.value && endTime.value) {
    const start = new Date(startTime.value)
    const end = new Date(endTime.value)
    if (start > end) {
      showToast('开始时间不能大于结束时间')
      return
    }
  }

  showDateBottom.value = false
}

const sureTiem = () => {
  if (currentTimeIndex.value !== 4) {
    timeValue.value = timeList[currentTimeIndex.value].value
    showBottom.value = false
    loadIndexData()
  } else {
    if (startTime.value && endTime.value) {
      timeValue.value = `${startTime.value} - ${endTime.value}`
      showBottom.value = false
      loadIndexData()
    } else {
      showToast('您有自定义时间未选择')
    }
  }

}

const timeValue = ref(timeList[0].value)

const searchQuery = ref('')

const tabsList = ref([])

const activeTabs = ref(0)

const handleTabsClick = (item, index) => {
  activeTabs.value = index
  currentGroupId.value = item.groupId
  loadIndexData()
}

const infoContent = ref('同比-去年同天，环比-上周同天')

const indexData = ref({})

const cruIndexMap = ref({
            "saleQty": "60.000000",
            "stockQty": "1709.000000",
            "saleQtyRate": "0.500000",
            "stockDay": 0,
            "saleVGrRate": "0.336485",
            "saleValueRate": "0.524268",
            "saleQGrRate": "0.333334",
            "dataFormat": "2.2",
            "stockDayGrRate": 0,
            "saleValue": "167.020000"
        })

const goodsList = ref([
  {
    "StockQty": "0.00",
    "SaleQty": "0.00",
    "IndexFlag": "3",
    "isFocus": 0,
    "GrowthRate": "0.00",
    "BarCode": "4891028703914",
    "sort": 3,
    "SaleValue": "0.00",
    "focusId": "CAF0A541A45943E6B39116E8D094B3CF",
    "GoodsID": "122292",
    "GoodsName": "商品名称"
},
])


// 加载首页数据
const loadIndexData = () => {
  selectGoodFocusIndex({
    appVer: '2.4.8',
    groupId: currentGroupId.value,
    selectDate: startTime.value,
    selectEndDate: endTime.value,
  })
  .then((res) => {
    console.log(res, 'res-selectGoodFocusIndex')
    indexData.value = res
    cruIndexMap.value = res.cruIndexMap
    goodsList.value = res.focusCruIndexList
  })
  .catch((err) => {
    console.log(err, 'err-selectGoodFocusIndex')
  })
}
// 加载商品关注组
const loadFocusGroup = (goodsId) => {
  selectUserGoodsGroupList(goodsId ? goodsId : "")
  .then((res) => {
    // console.log(res, 'res-selectUserGoodsGroupList')
    tabsList.value = res
    console.log(tabsList, 'tabsList')
    if (res && res.length > 0) {
      currentGroupId.value = tabsList.value[0].groupId
    }
    console.log(currentGroupId, 'currentGroupId')
    loadIndexData()
  })
  .catch((err) => {
    console.log(err, 'err-selectUserGoodsGroupList')
  })
}
// 加载关注组指标
const loadGroupMetrics = () => {}
// 加载关注组内商品
const loadGroupGoods = () => {}

// 抄送分享
const shareUser = () => {
  console.log('route to shareUser========')
  router.push({
    path: '/goodsFocus/shareUser',
    query: {
        g: currentGroupId.value
    }
  })
}

//============ 分组管理 ========================
const showManagerGroup = ref(false)
const showManagerGoods = ref(false)
const groupManagerDialogVisible = ref(false)
const groupTitle = ref('')
const groupEditDialogTitle = ref('')
const editGroupId = ref('')

const handleManagerGroup = () => {
  showManagerGroup.value = true
}
const groups = reactive([
  { name: "我的关注", id: 0 },
  { name: "七夕单品", id: 1 },
  { name: "活动促销", id: 2 },
  { name: "中秋礼盒", id: 3 },
]);

//拖拽结束的事件
const onEndSortGroup = async() => {
  const ids = groups.map((item) => item.id);
  await sortGroup({ ids })
};

// 添加分组
const handleAddGroup = () => {
  // 检查是否已有20个分组，最多可添加20个分组
  if(groups.length >= 20){
    showToast('最多可添加20个商品组合')
    return
  }
  groupTitle.value = ''
  groupEditDialogTitle.value = '添加组合'
  groupManagerDialogVisible.value = true
};

// 编辑分组
const handleEditGroup = (data) => {
  groupTitle.value = data.name
  editGroupId.value = data.id
  groupEditDialogTitle.value = '编辑组合'
  groupManagerDialogVisible.value = true
};

// 删除分组
const handleDeleteGroup = (data) => {
  showConfirmDialog({
    title: '删除分组',
    message: () =>
      h('span', [
        '您确定要删除 ',
        h('span', { style: { color: 'red', fontWeight: 'bold' } }, data.name),
        ' 分组吗？',
      ]),
  })
  .then(async () => {
    // on confirm
    const res = await deleteGroup(data.id)
    const index = groups.findIndex(group => group.id === data.id);
    if (index !== -1) {
      groups.splice(index, 1);
    }
  })
  .catch(() => {
    // on cancel do nothing
  });
}

//表单校验阻止关闭弹窗
const beforeClose = async (action) => {
  if (action === "confirm") {
    try {
      if(groupEditDialogTitle.value === '添加组合'){
        if(groupTitle.value.length > 10){
          showToast('组合请不要超过10个字符')
          return false
        }
        const id = await addGroup({'name': groupTitle.value})
        groups.push({'name': groupTitle.value, 'id': id})
        return true;
      } else if(groupEditDialogTitle.value === '编辑组合') {
        const res = await updateGroup({
          "description": groupTitle.value,
          "groupId": editGroupId.value,
          "isDefault":"",
          "name": groupTitle.value
        })
        // 修改标题
        groups.map((item) => {
          if(item.id == editGroupId.value) {
            item.name = groupTitle.value
          }
        })
        return true
      }
    } catch (error) {
      console.log(error)
      const msg = groupEditDialogTitle.value == '添加组合' ? '组合添加失败' : '组合编辑失败'
      showToast(msg)
      return true
    }
  } else {
    return true
  }
}
//============ 分组管理 ========================

//============ 商品管理 ========================
const checkedGoodsCnt = ref(0)
const groupChecked = ref(false)

// 编辑商品
const handleManagerGoods = () => {
  showManagerGoods.value = true
}

// 全选
const checkAll = () => {
  goodsList.value.forEach(goods => {
    goods.selected = groupChecked.value
  })
  countCheckedGoods()
}

// 选中商品
const handleSelectGoods = (goods) => {
  goods.selected = !goods.selected
  countCheckedGoods()
}

// 统计选中商品数量
const countCheckedGoods = () => {
  checkedGoodsCnt.value = 0
  goodsList.value.forEach(goods => {
    if (goods.selected) {
      checkedGoodsCnt.value++
    }
  })
  if(groupChecked.value == true && checkedGoodsCnt.value != goodsList.value.length){
    groupChecked.value = false
  }
}

// 删除商品
const handleDeleteGoods = () => {
  if (checkedGoodsCnt < 1) return
  showConfirmDialog({
    title: '删除分组内商品',
    message: '您确定要删除吗？',
  })
  .then(async () => {
    // on confirm
    const goodsIds = goodsList.value.filter(item => item.selected).map(item => item.GoodsId)
    const res = await deleteGroupGoods({
      goodsIds: goodsIds,
      groupId: currentGroupId.value
    })
    goodsList.value.map((item, index) => {
      if (item.selected) {
        goodsList.value.splice(index, 1);
      }
    })
  })
  .catch(() => {
    // on cancel do nothing
  });
}

// 排序商品
const onEndSortGoods = async() => {
  const focusIds = goodsList.value.map(goods => goods.focusId)
  await sortGroupGoods(focusIds)
}

// 置顶
const handleMoveTop = async(index) => {
  if (index === 0) return; // 已在顶部则忽略

  const item = goodsList.value[index];
  goodsList.value.splice(index, 1);    // 移除原位置的元素
  goodsList.value.unshift(item);       // 添加到数组开头

  // 更新排序
  await sortGroupGoods(goodsList.value.map(goods => goods.focusId))
}

// 进入商品详情
const handleGoodsClick = (item) => {
  router.push({
    path: '/goodsInfo',
    query: {
      t: sessionStorage.getItem('token'),
      u: sessionStorage.getItem('userId'),
      g: item.GoodsID
    }
  })
};

//============ 商品管理 ========================
//============ 商品搜索 ========================
const onSearch = () => {
  router.push({
    path: '/goodsFocus/searchGoods',
    query: {
        t: sessionStorage.getItem('token'),
        u: sessionStorage.getItem('userId'),
        g: currentGroupId.value
    }
  })
}
//============ 商品搜索 ========================

const getTodayString = () => {
  const date = new Date()
  const dateStr = `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`
  return dateStr
}

// 扫码
const scanCode = () => {
  wx.scanQRCode({
    needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
    scanType: ["qrCode","barCode"], // 可以指定扫二维码还是一维码，默认二者都有
    success: function (res) {
      console.log(res, 'res-scanQRCode')
      scanCodeResult.value = res.resultStr; // 当needResult 为 1 时，扫码返回的结果
    }
  })
}

const wxConfig = () => {
  api
    .getWxJsapiSignature(location.href.split('#')[0])
    .then((res) => {
      const result = res.result
      console.log(JSON.stringify(res))
      wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: result.appId, // 必填，公众号的唯一标识
        timestamp: result.timestamp, // 必填，生成签名的时间戳
        nonceStr: result.nonceStr, // 必填，生成签名的随机串
        signature: result.signature, // 必填，签名
        jsApiList: ['scanQRCode'], // 必填，需要使用的JS接口列表
      })
    })
    .catch((error) => {
      console.log(error)
    })
}

onBeforeMount(() => {
  // 组件挂载前执行，此时模板已编译但尚未挂载到DOM
  document.title = '商品关注'

  if (JSON.stringify(route.query) !== '{}') {
    if (
      typeof route.query.t !== 'undefined' &&
      typeof route.query.u !== 'undefined'
    ) {
      sessionStorage.setItem('token', route.query.t)
      sessionStorage.setItem('userId', route.query.u)
    }
  }
  wxConfig()
})

onMounted(() => {
  startTime.value = getTodayString()
  endTime.value = getTodayString()
  loadFocusGroup("")
  loadGroupMetrics()
  loadGroupGoods()
})
</script>

<template>
  <div class="product-follow">
    <!-- 日期选择器和搜索框 -->
    <div class="top-filter">
      <div class="time-box" @click="showBottom = !showBottom">
        <img class="top-filter-date-icon" src="../../assets/images/goodsFocus/date.png" />
        <div class="top-filter-date-text">{{ timeValue }}</div>
      </div>
      <div class="top-filter-input">
        <el-input v-model="searchQuery" placeholder="搜索并添加商品" :prefix-icon="Search" @focus="onSearch"/>
      </div>
    </div>

    <!-- 导航标签 -->
    <div class="tabs-box">
      <div class="tabs-box-item-group">
        <div
          v-for="(item, index) in tabsList"
          :key="index"
          class="tabs-box-items"
          :class="[activeTabs === index ? 'tabs-box-items-activation' : '']"
          @click="handleTabsClick(item, index)"
        >
          {{ item.groupName }}
        </div>
      </div>
      <div class="tabs-box-item tabs-box-item-activation" @click="handleManagerGroup()">
        分组管理<van-icon name="arrow-down" />
      </div>
    </div>

    <!-- 数据概览 -->
    <div class="data-overview">
      <div class="info">
        <van-icon name="info-o" class="info-icon" /><span class="info-content">{{
          infoContent
        }}</span>
      </div>
      <div class="overview-stats">
        <div class="grid-content" >
          <h2>{{ (Number(cruIndexMap.saleQty) || 0).toFixed(2) }}</h2>
          <p class="title">销量</p>
          <p class="year-on-year">同比：{{ (Number(cruIndexMap.saleQtyRate) * 100 || 0).toFixed(2) }}</p>
          <p class="monopoly">环比:{{ (Number(cruIndexMap.saleQGrRate) * 100 || 0).toFixed(2) }}</p>
        </div>
        <div class="grid-content" >
          <h2>{{ (Number(cruIndexMap.saleValue) || 0).toFixed(2) }}</h2>
          <p class="title">销售额</p>
          <p class="year-on-year">同比：{{ (Number(cruIndexMap.saleValueRate) * 100 || 0).toFixed(2) }}</p>
          <p class="monopoly">环比:{{ (Number(cruIndexMap.saleVGrRate) * 100 || 0).toFixed(2) }}</p>
        </div>
        <div class="grid-content" >
          <h2>{{ (Number(cruIndexMap.stockQty) || 0).toFixed(2) }}</h2>
          <p class="title">库存量</p>
          <p class="year-on-year">同比：{{ (Number(cruIndexMap.stockDay) * 100 || 0).toFixed(2) }}</p>
          <p class="monopoly">环比:{{ (Number(cruIndexMap.stockDayGrRate) * 100 || 0).toFixed(2) }}</p>
        </div>
      </div>
    </div>

    <!-- 商品列表 -->
    <div class="product-list">
      <div class="product-list-header" @click="handleManagerGoods">
        <div class="left">
          <img class="icon" src="../../assets/images/goodsFocus/edit.png" />
          <span>编辑商品</span>
        </div>
        <div class="right" @click="shareUser">
          <img class="icon" src="../../assets/images/goodsFocus/share.png" />
          <span>抄送分享</span>
        </div>
      </div>

      <div v-for="goods in goodsList" :key="goods.GoodsID" class="product-item" @click="handleGoodsClick(goods)">
        <div class="product-item-header">
          <div class="product-image">
            <img :src="goods.GoodsImg || defaultImg" alt="Product Image" style="width: 100%" />
          </div>
          <div class="product-details">
            <h4>
              <span class="tag">
                <van-tag
                  :type="{
                    1: 'danger',
                    2: 'warning',
                    3: 'success'
                  }[goods.IndexFlag]"
                >
                  {{ { 1: 'A', 2: 'B', 3: 'C' }[goods.IndexFlag] }}
                </van-tag>
              </span>
              <span class="name">{{ goods.GoodsName }}</span>
            </h4>
            <div class="product-info">
              <p>编码：{{ goods.GoodsID }}</p>
              <p>条码：{{ goods.BarCode || '' }}</p>
            </div>
          </div>
        </div>
        <div class="product-item-bottom">
          <div class="product-item-bottom-item">
            <h3>{{ goods.SaleValue || 0.00 }}</h3>
            <small>销售额</small>
          </div>
          <div class="product-item-bottom-item">
            <h3>{{ goods.SaleQty || 0.00 }}</h3>
            <small>销量</small>
          </div>
          <div class="product-item-bottom-item">
            <h3>{{ goods.stockQty || 0.00 }}</h3>
            <small>库存量</small>
          </div>
        </div>
      </div>
    </div>
    <van-popup
      v-model:show="showBottom"
      position="bottom"
      :style="{ height: '40%' }"
      :close-on-click-overlay="false"
      closeable
    >
      <p class="filter-title">默认筛选</p>
      <div class="time-box-list">
        <van-button
          :type="index === currentTimeIndex ? 'primary' : 'default'"
          v-for="(item, index) in timeList"
          :key="index"
          size="small"
          @click="timeChange(item, index)"
        >
          {{ item.label }}</van-button
        >
      </div>
      <p class="filter-title">自定义筛选</p>
      <div class="customize-tiem-box">
        <el-input
          v-model="startTime"
          placeholder="开始时间"
          readonly
          @click="timeChange('startTime', 4)"
        />
        至
        <el-input
          v-model="endTime"
          placeholder="结束时间"
          readonly
          @click="timeChange('endTime', 4)"
        />
      </div>
      <div class="customize-tiem-button">
        <van-button type="default" @click="showBottom = false">取消 </van-button>
        <van-button type="primary" @click="sureTiem"> 确定</van-button>
      </div>
    </van-popup>
    <van-popup
      v-model:show="showDateBottom"
      position="bottom"
      :style="{ height: '40%' }"
      :close-on-click-overlay="false"
      ><van-date-picker
        v-model="currentDate"
        title="选择年月日"
        :columns-type="columnsType"
        :formatter="formatter"
        @cancel="showDateBottom = false"
        @confirm="confirmDate"
    /></van-popup>

    <!-- 分组管理右侧抽屉 -->
    <van-popup
      v-model:show="showManagerGroup"
      position="right"
      :style="{ width: '75%', height: '100%' }"
    >
      <van-button icon="plus" type="primary" class="group-manager-top-btn" block @click="handleAddGroup">创建组合</van-button>
      <draggable
        :list="tabsList"
        class="group-manager-item-wrap"
        ghost-class="ghost"
        chosen-class="chosenClass"
        animation="300"
        @end="onEndSortGroup"
        item-key="groupId"
      >
        <template #item="{ element }">
          <div class="item">
            <div class="item-text">
              {{ element.groupName }}
            </div>
            <div class="item-btn">
              <van-button icon="edit" type="primary" size="small" plain hairline @click="handleEditGroup(element)">编辑</van-button>
              <van-button icon="delete" type="danger" size="small" plain hairline @click="handleDeleteGroup(element)">删除</van-button>
            </div>
          </div>
        </template>
      </draggable>
      <div class="group-manager-tip">最多可添加20个组合，长按可拖动排序</div>
    </van-popup>

    <van-dialog
      v-model:show="groupManagerDialogVisible"
      :title="groupEditDialogTitle"
      show-cancel-button
      :before-close="beforeClose"
    >
        <van-field
          v-model="groupTitle"
          placeholder="请输入分组名称"
          name="title"
        />
    </van-dialog>
    <!-- 分组管理右侧抽屉 -->

    <!-- 编辑商品 -->
     <van-popup
      v-model:show="showManagerGoods"
      position="right"
      :style="{ width: '75%', height: '100%' }"
    >
      <div class="goods-manager">
        <div class="goods-manager-top">
          <div class="goods-manager-top-radio">
            <van-field name="check" @click="checkAll">
              <template #input>
                <van-checkbox v-model="groupChecked" shape="square" /> 全选
              </template>
            </van-field>
          </div>
          <div class="goods-manager-top-info">
            已选择 <b>{{ checkedGoodsCnt }}</b> 个
          </div>
        </div>
        <div class="goods-manager-list">
          <draggable
            :list="goodsList"
            ghost-class="ghost"
            chosen-class="chosenClass"
            animation="300"
            @end="onEndSortGoods"
            item-key="focusId"
          >
            <template #item="{ element, index }">
              <div class="item" @click="handleSelectGoods(element)">
                <div class="item-image">
                  <img :src="element?.GoodsImg || defaultImg" alt="Product Image" style="width: 100%" />
                </div>
                <div class="item-details">
                  <h4>
                    <span class="tag">
                      <van-tag
                        :type="{
                          1: 'danger',
                          2: 'warning',
                          3: 'success'
                        }[element.IndexFlag]"
                      >
                        {{ { 1: 'A', 2: 'B', 3: 'C' }[element.IndexFlag] }}
                      </van-tag>
                    </span>
                    <span class="name">{{ element.GoodsName }}</span>
                  </h4>
                  <div class="item-info">
                    <p>编码：{{ element.GoodsId || '' }}</p>
                    <p>条码：{{ element.BarCode || '' }}</p>
                  </div>
                  <div @click.stop="handleMoveTop(index)">置顶</div>
                </div>
                <div :class="['selected-badge', { 'visible': element.selected }]">
                  <img src="@/assets/images/goodsFocus/selected.png" alt="Description">
                </div>
              </div>
            </template>
          </draggable>
        </div>
        <div class="goods-manager-btn">
          <van-button plain type="danger" size="small" @click="handleDeleteGoods">删除商品</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<style scoped lang="less">
.product-follow {
  padding: 20px;
  .top-filter {
    padding: 0 0 10px 0;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    border-bottom: solid 1px #ececec;
    .time-box {
      width: 50%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .top-filter-date-icon {
        width: 50px;
        height: 50px;
      }
      .top-filter-date-text {
        font-size: 26px;
      }
    }
    .top-filter-input {
      width: 50%;
    }
  }
  .tabs-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 10px;
    background-color: #f2f2f2;
    margin: 0 0 30px 0;
    overflow: hidden;
    .tabs-box-item-group {
      width: 80%;
      overflow: hidden;
      overflow-x: auto;
      white-space: nowrap;
      -webkit-overflow-scrolling: touch; /* 提升移动端滚动体验 */
      scrollbar-width: none; /* 隐藏滚动条 Firefox */
      &::-webkit-scrollbar {
        display: none; /* 隐藏滚动条 Chrome/Safari */
      }
      .tabs-box-items {
        width: 33%;
        height: 50px;
        display: inline-block;
        text-align: center;
        line-height: 50px;
        font-size: 22px;
        border-radius: 10px;
      }
      .tabs-box-items-activation {
        background-color: #02a7f0;
        color: #fff;
      }
    }
    .tabs-box-item {
      width: 20%;
      height: 50px;
      text-align: center;
      line-height: 50px;
      font-size: 22px;
      border-radius: 10px;
    }
    .tabs-box-item-activation {
      background-color: #02a7f0;
      color: #fff;
    }
  }
}

.data-overview {
  margin: 0 0 30px 0;
  .info {
    display: flex;
    align-items: center;
    margin: 0 0 15px 0;
    .info-icon {
      color: #fd6c18;
    }
    .info-content {
      margin: 0 0 0 15px;
      font-size: 24px;
    }
  }
  .overview-stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .grid-content {
      width: 220px;
      background-color: #f2f2f2;
      border-radius: 10px;
      text-align: center;
      padding: 15px 25px 10px 25px;
      h2 {
        margin: 0 0 15px 0;
      }
      .title {
        font-size: 30px;
        margin: 0 0 10px 0;
      }
      .year-on-year,
      .monopoly {
        font-size: 20px;
        margin: 0 0 5px 0;
        color: #bb927d;
      }
    }
  }
}

.product-list .product-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 0 30px 0;

  .left {
    width: 50%;
    font-size: 24px;
    display: flex;
    align-items: center;
    .icon {
      width: 30px;
      height: 30px;
      margin-right: 0.4rem;
    }
  }

  .right {
    margin-left: auto;
    font-size: 24px;
    display: flex;
    align-items: center;
    .icon {
      width: 30px;
      height: 30px;
      margin-right: 0.4rem;
    }
  }
}

.product-list {
  .product-item {
    padding: 10px;
    margin: 0 0 15px 0;
    border-radius: 5px;
    box-shadow: 0px 2px 20px rgba(153, 153, 153, 0.35);
    .product-item-header {
      display: flex;
      margin: 0 0 10px 0;
      .product-image {
        width: 200px;
        height: 200px;
        margin: 0 15px 0 0;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .product-details {
        flex: 2;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin: 0 15px 0 0;
        h4 {
          margin: 10px 0 0 0;
          display: flex;
          align-items: center;
          span {
            display: inline-block;
          }
          .tag {
            padding: 10px 15px;
            background-color: #f2f2f2;
            margin: 0 10px 0 0;
          }
          .name {
            font-size: 24px;
          }
        }
        .product-info {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin: 0 0 10px 0;
          p {
            font-size: 24px;
            color: #bb927d;
          }
        }
      }
    }
    .product-item-bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .product-item-bottom-item {
        width: 30%;
        text-align: center;
        small {
          color: #bb927d;
        }
      }
    }
  }
}
.filter-title {
  margin: 80px 20px 20px 20px;
}
.time-box-list {
  margin: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  button {
    width: 19%;
  }
}
.customize-tiem-box {
  margin: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  :deep(.el-input) {
    width: 40%;
  }
}
.customize-tiem-button {
  margin: 60px 20px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  button {
    width: 35%;
  }
}
.group-manager-top-btn {
  width: 95%;
  margin: 1.67vw;
  height: 9.67vw;
}
.group-manager-item-wrap {
  .item {
    border: solid 1px #eee;
    border-radius: 4px;
    padding: 1.8vw;
    text-align: left;
    width: 95%;
    margin-left: 1.67vw;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .item-btn {
      margin-left: auto;
    }
  }

  .item:hover {
    cursor: move;
  }

  .item+.item {
    margin-top: 10px;
  }
}
.group-manager-tip {
  text-align: center;
  margin-top: 5px;
  margin-bottom: 5px;
}

// 商品管理
.goods-manager {

  .goods-manager-top {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .goods-manager-top-info {
      margin-left: auto;
      margin-right: 2.33vw;
    }
  }

  .item {
    padding: 10px;
    margin: 0 2vw 2vw 2vw;
    border-radius: 5px;
    box-shadow: 0px 2px 20px rgba(153, 153, 153, 0.35);
    display: flex;
    position: relative;

    .item-image {
      width: 200px;
      height: 200px;
      margin: 0 15px 0 0;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .item-details {
      flex: 2;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin: 0 15px 0 0;
      h4 {
        margin: 10px 0 0 0;
        display: flex;
        align-items: center;
        span {
          display: inline-block;
        }
        .tag {
          padding: 10px 15px;
          background-color: #f2f2f2;
          margin: 0 10px 0 0;
        }
        .name {
          font-size: 24px;
        }
      }
      .item-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 0 0 10px 0;
        p {
          font-size: 24px;
          color: #bb927d;
        }
      }
    }
    .selected-badge {
      display: none;
      position: absolute;
      top: 0;
      right: 7.27vw;
      width: 0;
      height: 0;
    }
    .visible {
      display: block;
    }
  }

}
</style>
