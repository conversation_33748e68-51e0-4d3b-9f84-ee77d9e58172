/**
 * 基础折线图配置
 * @param {Array} item - 图表数据数组
 * @param {Object} val - 图表配置对象
 * @returns {Object} 包含标题、图表配置和状态的完整配置对象
 */
// 判断是否为移动端
const isMobile = window.innerWidth < 768

export const createLineChart = (item, val) => {
  // 创建基础折线图配置对象
  console.log(1)
  let chartConfig = {
    grid: {
      top: isMobile ? '10%' : '3%',
      left: isMobile ? '5%' : '3%',
      right: isMobile ? '5%' : '4%',
      bottom: isMobile ? '10%' : '3%',
      containLabel: true,
    },
    // 提示框配置
    tooltip: {
      trigger: 'axis', // 触发类型为坐标轴
      axisPointer: {
        type: 'shadow', // 指示器类型为阴影
      },
      // formatter: '{c}', // 提示框内容格式
      valueFormatter: (value) => value.toFixed(2), // 数值显示部分的格式化
    },
    // X轴配置
    xAxis: {
      type: 'category', // 类目轴
      boundaryGap: false, // 坐标轴两边留白策略
      data: [], // 类目数据
    },
    // Y轴配置
    yAxis: {
      type: 'value', // 数值轴
    },
    // 系列配置
    series: [
      {
        data: [], // 数据数组
        type: 'line', // 图表类型为折线图
        areaStyle: {
          color: '#3F92FF', // 区域填充颜色
        },
        itemStyle: {
          color: '#3F92FF', // 折线颜色
          innerWidth: 5, // 折线宽度
        },
      },
    ],
  }

  // 移除表头数据
  item.splice(0, 1)

  // 填充数据
  item.forEach((item) => {
    chartConfig.xAxis.data.push(item.sdate)
    chartConfig.series[0].data.push(item.dvalue || 0)
  })

  return {
    title: val ? val.attrValue : '', // val.DefaultV,
    chart: chartConfig,
    status: true,
  }
}

/**
 * 基础柱状图配置方法
 * @param {Array} item - 图表数据数组
 * @param {Object} val - 图表配置对象
 * @returns {Object} 包含标题、图表配置和状态的完整配置对象
 */
export const createBasicBarChart = (item, val) => {
  console.log(2)
  // 创建基础柱状图配置对象
  let chartConfig = {
    grid: {
      top: isMobile ? '10%' : '3%',
      left: isMobile ? '5%' : '3%',
      right: isMobile ? '5%' : '4%',
      bottom: isMobile ? '10%' : '3%',
      containLabel: true,
    },
    legend: {},
    tooltip: {},
    dataset: {
      source: [],
    },
    color: ['#3F92FF', '#FFCB3F', '#72D102', '#4B64F4'],
    xAxis: {
      type: 'category',
      data: [], // 添加 x 轴数据数组
    },
    yAxis: {
      name: item[0].Z_1,
      splitNumber: 10,
    },
    series: [],
  }

  // 根据数据列数创建对应的系列
  for (let c = 0; c < item[0].Z_3; c++) {
    chartConfig.series.push({
      type: 'bar',
      barMaxWidth: 25,
    })
  }

  // 移除表头数据
  // item.splice(0, 1)

  // 处理数据源
  for (let i = 0; i < item.length; i++) {
    let rowData = []
    if (item[i].a_0 !== 'X-轴') {
      rowData.push(item[i].a_0) // 使用 a_0 作为 x 轴数据
      chartConfig.xAxis.data.push(item[i].a_0) // 添加到 x 轴数据数组
    }

    for (let c = 0; c < chartConfig.series.length; c++) {
      let key = 'd_' + (c + 1)
      if (!item[i][key]) {
        item[i][key] = 0
      }
      if (item[i].a_0 !== 'X-轴') {
        rowData.push(item[i][key])
      }
    }
    chartConfig.dataset.source.push(rowData)
  }

  return {
    title: val.DefaultV,
    chart: chartConfig,
    status: true,
  }
}

/**
 * 堆叠折线图配置方法
 * @param {Array} item - 图表数据数组
 * @param {Object} val - 图表配置对象
 * @returns {Object} 包含标题、图表配置和状态的完整配置对象
 */
export const createStackedLineChart = (item, val) => {
  // 创建堆叠折线图配置对象
  console.log(3)
  let chartConfig = {
    title: {
      text: '折线图堆叠',
    },
    color: [
      '#3F92FF',
      '#FFCB3F',
      '#FF8000',
      '#72D102',
      '#50B2C1',
      '#50B2C1',
      '#7D60FF',
      '#5346B1',
      '#76C99A',
    ],
    grid: {
      top: isMobile ? '10%' : '3%',
      left: isMobile ? '5%' : '3%',
      right: isMobile ? '5%' : '4%',
      bottom: isMobile ? '10%' : '3%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: [],
      bottom: isMobile ? 0 : 'auto',
    },
    xAxis: {
      type: 'category',
      data: [],
    },
    yAxis: {
      min: function (value) {
        return value.min
      },
      type: 'value',
    },
    series: [],
  }

  // 设置标题
  chartConfig.title.text = item[0].Z_1

  // 添加图例数据
  for (let c = 0; c < item[0].Z_3; c++) {
    let key = 'd_' + (c + 1)
    chartConfig.legend.data.push(item[0][key])
  }

  // 移除表头数据
  item.splice(0, 1)

  // 添加X轴数据
  for (let c = 0; c < item.length; c++) {
    chartConfig.xAxis.data.push(item[c].a_0)
  }

  // 创建系列配置
  chartConfig.legend.data.forEach((item) => {
    chartConfig.series.push({
      name: item,
      data: [],
      type: 'line',
      smooth: true,
      itemStyle: {
        normal: {
          lineStyle: {
            width: 3,
          },
        },
      },
    })
  })

  // 填充系列数据
  for (let i = 0; i < chartConfig.series.length; i++) {
    let key = 'd_' + (i + 1)
    for (let n = 0; n < item.length; n++) {
      chartConfig.series[i].data.push(item[n][key])
    }
  }

  return {
    title: val.DefaultV,
    chart: chartConfig,
    status: true,
  }
}

/**
 * 柱线混合图配置方法
 * @param {Array} item - 图表数据数组
 * @param {Object} val - 图表配置对象
 * @returns {Object} 包含标题、图表配置和状态的完整配置对象
 */
export const createBarLineMixedChart = (item, val) => {
  // 获取配置参数
  console.log(4)
  let Z_4 = item[0].Z_4
  let Z_5 = item[0].Z_5
  let min1 = '',
    min2 = '',
    max1 = '',
    max2 = '',
    xOne = '',
    xTwo = ''
  // 创建柱线混合图配置对象
  let chartConfig = {
    grid: {
      top: isMobile ? '10%' : '3%',
      left: isMobile ? '5%' : '3%',
      right: isMobile ? '5%' : '4%',
      bottom: isMobile ? '10%' : '3%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
    },
    legend: {
      data: [],
    },
    xAxis: [
      {
        type: 'category',
        data: [],
        axisPointer: {
          type: 'shadow',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '',
        axisLabel: {
          formatter: '{value}',
        },
        splitNumber: 5,
        min: '',
        max: '',
        interval: '',
      },
      {
        type: 'value',
        name: '',
        axisLabel: {
          formatter: '{value}',
        },
        splitNumber: 5,
        min: '',
        max: '',
        interval: '',
      },
    ],
    series: [
      {
        name: '',
        type: 'bar',
        itemStyle: {
          color: '#3F92FF',
        },
        data: [],
      },
      {
        name: '',
        type: 'line',
        smooth: true,
        yAxisIndex: 1,
        itemStyle: {
          normal: {
            color: '#FFCB3F',
            lineStyle: {
              width: 3,
            },
          },
        },
        data: [],
      },
    ],
  }

  // 设置图例数据
  chartConfig.legend.data.push(item[0][Z_4])
  chartConfig.legend.data.push(item[0][Z_5])

  // 设置Y轴名称
  chartConfig.yAxis[0].name = item[0].Z_1.split(',')[0]
  chartConfig.yAxis[1].name = item[0].Z_1.split(',')[1]

  // 设置系列名称
  chartConfig.series[0].name = item[0].Z_1.split(',')[0]
  chartConfig.series[1].name = item[0].Z_1.split(',')[1]

  // 移除表头数据
  item.splice(0, 1)

  // 填充数据
  item.forEach((item) => {
    chartConfig.xAxis[0].data.push(item.a_0)
    chartConfig.series[0].data.push(item[Z_4])
    chartConfig.series[1].data.push(item[Z_5])
  })

  // 计算最大值和最小值
  function _getMaxValue(arr) {
    const max = Math.max(...arr)
    return Math.ceil(max / 9.5) * 10
  }
  function _getMinValue(arr) {
    const min = Math.min(...arr)
    return Math.floor(min / 12) * 10
  }

  // 设置Y轴范围
  min1 = _getMinValue(chartConfig.series[0].data)
  min2 = _getMinValue(chartConfig.series[1].data)
  max1 = _getMaxValue(chartConfig.series[0].data)
  max2 = _getMaxValue(chartConfig.series[1].data)
  xOne = max1 - min1
  xTwo = max2 - min2

  // 更新Y轴配置
  chartConfig.yAxis[0].min = min1
  chartConfig.yAxis[0].max = max1
  chartConfig.yAxis[0].interval = Math.ceil(xOne / 10)
  chartConfig.yAxis[1].min = min2
  chartConfig.yAxis[1].max = max2
  chartConfig.yAxis[1].interval = Math.ceil(xTwo / 10)

  return {
    title: val.DefaultV,
    chart: chartConfig,
    status: true,
  }
}

/**
 * 堆叠柱状图配置方法
 * @param {Array} item - 图表数据数组
 * @param {Object} val - 图表配置对象
 * @returns {Object} 包含标题、图表配置和状态的完整配置对象
 */
export const createStackedBarChart = (item, val) => {
  // 获取堆叠配置
  console.log(5)
  let stack = item[0].Z_1
  // 创建堆叠柱状图配置对象
  let chartConfig = {
    grid: {
      top: isMobile ? '10%' : '3%',
      left: isMobile ? '5%' : '3%',
      right: isMobile ? '5%' : '4%',
      bottom: isMobile ? '10%' : '3%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    color: [
      '#72D102',
      '#4B64F4',
      '#FFBE00',
      '#50B2C1',
      '#3F92FF',
      '#1DE9B6',
      '#FF8000',
      '#7D60FF',
      '#5346B1',
      '#76C99A',
    ],
    legend: {
      data: [],
    },
    xAxis: [
      {
        type: 'category',
        data: [],
      },
    ],
    yAxis: [
      {
        type: 'value',
      },
    ],
    series: [],
  }

  // 添加图例数据
  for (let c = 0; c < item[0].Z_3; c++) {
    let key = 'd_' + (c + 1)
    chartConfig.legend.data.push(item[0][key])
  }

  // 移除表头数据
  item.splice(0, 1)

  // 创建系列配置
  chartConfig.legend.data.forEach((item) => {
    chartConfig.series.push({
      name: item,
      type: 'bar',
      stack: stack,
      data: [],
    })
  })

  // 添加X轴数据
  item.forEach((item) => {
    chartConfig.xAxis[0].data.push(item.sdate)
  })

  // 填充系列数据
  for (let i = 0; i < chartConfig.series.length; i++) {
    let key = 'd_' + (i + 1)
    for (let n = 0; n < item.length; n++) {
      chartConfig.series[i].data.push(item[n][key])
    }
  }

  return {
    title: val.DefaultV,
    chart: chartConfig,
    status: true,
  }
}

/**
 * 饼图配置方法
 * @param {Array} item - 图表数据数组
 * @param {Object} val - 图表配置对象
 * @returns {Object} 包含标题、图表配置和状态的完整配置对象
 */
export const createPieChart = (item, val) => {
  console.log(6)
  // 创建饼图配置对象
  let chartConfig = {
    legend: {
      orient: isMobile ? 'horizontal' : 'vertical',
      left: isMobile ? 'center' : 'left',
      top: isMobile ? 'bottom' : 'middle',
      type: isMobile ? 'scroll' : 'plain',
    },
    color: [
      '#FF4E69',
      '#7347F5',
      '#32CF88',
      '#FFBE00',
      '#2C68FF',
      '#FFA0E1',
      '#47CCFF',
      '#7F97FF',
      '#FF9327',
    ],
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)',
    },
    series: [
      {
        name: '',
        type: 'pie',
        radius: '60%',
        center: isMobile ? ['50%', '40%'] : ['50%', '50%'],
        data: [],
        itemStyle: {
          emphasis: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
          normal: {
            label: {
              show: true,
              formatter: '{b}\n{d}%',
              overflow: 'none', // 标签超出不省略
            },
            labelLine: { show: true },
          },
        },
      },
    ],
  }
  // 设置系列名称
  chartConfig.series[0].name = item[0].Z_1
  // 移除表头数据
  item.splice(0, 1)
  // 填充数据
  item.forEach((item) => {
    chartConfig.series[0].data.push({
      value: item.d_1,
      name: item.a_0, // 使用 a_0 作为数据项名称
    })
  })
  return {
    title: val.DefaultV,
    chart: chartConfig,
    status: true,
  }
}

/**
 * 环形图配置方法
 * @param {Array} item - 图表数据数组
 * @param {Object} val - 图表配置对象
 * @returns {Object} 包含标题、图表配置和状态的完整配置对象
 */
export const createRingChart = (item, val) => {
  console.log(7)
  // 创建环形图配置对象
  let chartConfig = {
    legend: {
      top: '5%',
      left: '5%',
    },
    color: [
      '#FF4E69',
      '#7347F5',
      '#32CF88',
      '#FFBE00',
      '#2C68FF',
      '#FFA0E1',
      '#47CCFF',
      '#7F97FF',
      '#FF9327',
    ],
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)',
    },
    series: [
      {
        name: '',
        type: 'pie',
        radius: ['40%', '70%'],
        data: [],
        itemStyle: {
          emphasis: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
          normal: {
            label: {
              show: true,
              formatter: '{b} : {d}%',
            },
            labelLine: { show: true },
          },
        },
      },
    ],
  }

  // 设置系列名称
  chartConfig.series[0].name = item[0].Z_1

  // 移除表头数据
  item.splice(0, 1)

  // 填充数据
  item.forEach((item) => {
    chartConfig.series[0].data.push({
      value: item.d_1,
      name: item.sdate,
    })
  })

  return {
    title: val.DefaultV,
    chart: chartConfig,
    status: true,
  }
}

/**
 * 嵌套柱形图配置方法
 * @param {Array} item - 图表数据数组
 * @param {Object} val - 图表配置对象
 * @returns {Object} 包含标题、图表配置和状态的完整配置对象
 */
export const createNestedBarChart = (item, val) => {
  console.log(8)
  // 创建嵌套柱形图配置对象
  let chartConfig = {
    grid: {
      top: isMobile ? '10%' : '3%',
      left: isMobile ? '5%' : '3%',
      right: isMobile ? '5%' : '4%',
      bottom: isMobile ? '10%' : '3%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
    },
    color: [
      '#72D102',
      '#4B64F4',
      '#FFBE00',
      '#50B2C1',
      '#3F92FF',
      '#1DE9B6',
      '#FF8000',
      '#7D60FF',
      '#5346B1',
      '#76C99A',
    ],
    legend: {
      data: [],
    },
    xAxis: [
      {
        type: 'category',
        data: [],
        axisPointer: {
          type: 'shadow',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: item[0].Z_1,
        axisLabel: {
          formatter: '{value}',
        },
      },
    ],
    series: [],
  }

  // 创建系列配置
  for (let c = 0; c < item[0].Z_3; c++) {
    let key = 'd_' + (c + 1)
    chartConfig.legend.data.push(item[0][key])
    chartConfig.series.push({
      name: item[0][key],
      type: 'bar',
      data: [],
    })
  }

  // 移除表头数据
  item.splice(0, 1)

  // 添加X轴数据
  for (let i = 0; i < item.length; i++) {
    chartConfig.xAxis[0].data.push(item[i].a_0)
  }

  // 填充系列数据
  for (let n = 0; n < chartConfig.series.length; n++) {
    let key = 'd_' + (n + 1)
    item.forEach((item) => {
      chartConfig.series[n].data.push(item[key])
    })
  }

  return {
    title: val.DefaultV,
    chart: chartConfig,
    status: true,
  }
}

// 行业数据
export const industryDataLineChart = (
  legendData,
  legendSelected,
  timeData,
  seriesData,
  min,
  max,
) => {
  let chartConfig = {
    color: ['#ABD895', '#8397D6', '#FC9065', '#EF6E6E', '#FAC95D', '#52AE83', '#89CBE4', '#A36FBC'],
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      type: 'scroll',
      data: legendData,
      selected: legendSelected,
      show: false,
      textStyle: {
        fontSize: 12, // 文字大小
      },
    },
    grid: {
      left: '3%',
      right: '3%',
      top: '5%',
      bottom: '0%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: timeData,
      axisLabel: {
        // rotate: 60
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      min: min,
      max: max,
      splitNumber: 5,
      axisTick: {
        show: false,
      },
    },
    series: seriesData,
  }
  for (let i = 0; i < chartConfig.series.length; i++) {
    chartConfig.series[i].smooth = true
  }

  return {
    chart: chartConfig,
    status: true,
  }
}

/**
 * 柱状图加双折线图配置方法
 * @param {Object} data - 包含xAxis、barData、lineData1、lineData2的数据对象
 * @returns {Object} 包含图表配置和状态的完整配置对象
 */
//  goodsInfo 图表
export const createBarDoubleLineChart = (data, obj) => {
  // 创建图表配置对象
  let chartConfig = {
    grid: {
      left: isMobile ? '5%' : '3%',
      right: isMobile ? '5%' : '4%',
      bottom: isMobile ? '15%' : '3%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
      position: [0, 0],
      formatter: function (params) {
        let result = params[0].axisValue + '<br/>'
        params.forEach((item, index) => {
          if (index === 0) {
            // 销售额的tooltip
            const saleValueTB = data.saleValueTB ? data.saleValueTB[item.dataIndex] : ''
            const saleValueHB = data.SaleValueHB ? data.SaleValueHB[item.dataIndex] : ''
            result += item.seriesName + ': ' + item.value
            if (saleValueTB) {
              result += ' (同比: ' + (saleValueTB * 100).toFixed(2) + '%)'
            }
            if (saleValueHB) {
              result += ' (环比: ' + (saleValueHB * 100).toFixed(2) + '%)'
            }
            result += '<br/>'
          } else if (index === 1) {
            // 销量的tooltip
            const QtyTB = data.QtyTB ? data.QtyTB[item.dataIndex] : ''
            const QtyHB = data.QtyHB ? data.QtyHB[item.dataIndex] : ''
            result += item.seriesName + ': ' + item.value
            if (QtyTB) {
              result += ' (同比: ' + (QtyTB * 100).toFixed(2) + '%)'
            }
            if (QtyHB) {
              result += ' (环比: ' + (QtyHB * 100).toFixed(2) + '%)'
            }
            result += '<br/>'
          } else if (index === 2) {
            // 客单量的tooltip
            const AvgSheetQty = data.AvgSheetQty ? data.AvgSheetQty[item.dataIndex] : ''
            result += item.seriesName + ': ' + item.value
            if (AvgSheetQty) {
              result += ' (平均客单量: ' + AvgSheetQty + ')'
            }
            result += '<br/>'
          } else {
            // 其他数据的tooltip
            result += item.seriesName + ': ' + item.value + '<br/>'
          }
        })
        return result
      },
    },
    legend: {
      data: [obj.barName, obj.lineName1, obj.lineName2],
      bottom: isMobile ? 0 : 'auto',
    },
    xAxis: [
      {
        type: 'category',
        data: data.xAxis,
        axisPointer: {
          type: 'shadow',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '', //'销售额',
        position: 'left',
        axisLabel: {
          formatter: '{value} ', //元
        },
      },
      {
        type: 'value',
        name: '', //'数量',
        position: 'right',
        axisLabel: {
          formatter: '{value}',
        },
      },
    ],
    series: [
      {
        name: obj.barName,
        type: 'bar',
        data: data.barData,
        itemStyle: {
          color: '#3F92FF',
        },
      },
      {
        name: obj.lineName1,
        type: 'line',
        yAxisIndex: 1,
        data: data.lineData1,
        smooth: true,
        itemStyle: {
          color: '#FFCB3F',
        },
        lineStyle: {
          width: 3,
        },
      },
      {
        name: obj.lineName2,
        type: 'line',
        yAxisIndex: 1,
        data: data.lineData2,
        smooth: true,
        itemStyle: {
          color: '#72D102',
        },
        lineStyle: {
          width: 3,
        },
      },
    ],
  }

  return {
    chart: chartConfig,
    status: true,
  }
}

/**
 * 柱状图加单折线图配置方法
 * @param {Object} data - 包含xAxis、barData、lineData的数据对象
 * @param {Object} names - 包含barName和lineName的对象，用于设置图例名称
 * @returns {Object} 包含图表配置和状态的完整配置对象
 */
// goodsInfo 图表
export const createBarSingleLineChart = (data, obj, type) => {
  // 创建图表配置对象
  let chartConfig = {
    grid: {
      left: isMobile ? '5%' : '3%',
      right: isMobile ? '5%' : '4%',
      bottom: isMobile ? '15%' : '3%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
      position: [0, 0],
      formatter: function (params) {
        let result = params[0].axisValue + '<br/>'
        params.forEach((item, index) => {
          // 毛利率图表
          if (type === 'grossMargin') {
            if (index === 0) {
              // 毛利额的tooltip
              const ProfitTB = data.ProfitTB ? data.ProfitTB[item.dataIndex] : ''
              const ProfitHB = data.ProfitHB ? data.ProfitHB[item.dataIndex] : ''
              result += item.seriesName + ': ' + item.value.toFixed(2)
              if (ProfitTB) {
                result += ' (同比: ' + (ProfitTB * 100).toFixed(2) + '%)'
              }
              if (ProfitHB) {
                result += ' (环比: ' + (ProfitHB * 100).toFixed(2) + '%)'
              }
              result += '<br/>'
            } else if (index === 1) {
              // 毛利率的tooltip
              const ProfitRateTB = data.ProfitRateTB ? data.ProfitRateTB[item.dataIndex] : ''
              const ProfitRateHB = data.ProfitRateHB ? data.ProfitRateHB[item.dataIndex] : ''
              result += item.seriesName + ': ' + (item.value * 100).toFixed(2) + '%'
              if (ProfitRateTB) {
                result += ' (同比: ' + (ProfitRateTB * 100).toFixed(2) + '%)'
              }
              if (ProfitRateHB) {
                result += ' (环比: ' + (ProfitRateHB * 100).toFixed(2) + '%)'
              }
              result += '<br/>'
            }
          }
          // 库存图表
          else if (type === 'inventory') {
            if (index === 0) {
              // 库存金额的tooltip
              result += item.seriesName + ': ' + item.value.toFixed(2) + '<br/>'
              // 添加安全库存信息
              const safeCloseQty = data.SafeCloseQty ? data.SafeCloseQty[item.dataIndex] : ''
              if (safeCloseQty) {
                result += '安全库存: ' + safeCloseQty + '<br/>'
              }
            } else {
              // 库存数量的tooltip
              result += item.seriesName + ': ' + item.value + '<br/>'
            }
          }
          // 成本图表
          else if (type === 'cost') {
            if (index === 0) {
              // 金额的tooltip
              result += item.seriesName + ': ' + item.value.toFixed(2) + '<br/>'
              // 添加进价信息
              const cost = data.Cost ? data.Cost[item.dataIndex] : ''
              const avgCost = data.AvgCost ? data.AvgCost[item.dataIndex] : ''
              if (cost) {
                result += '当前进价: ' + cost.toFixed(2) + '<br/>'
              }
              if (avgCost) {
                result += '平均进价: ' + avgCost.toFixed(2) + '<br/>'
              }
            } else {
              // 进价偏移率的tooltip
              result += item.seriesName + ': ' + (item.value * 100).toFixed(2) + '%<br/>'
            }
          }
          // 其他图表
          // else {
          //   const valueTB = data.ProfitTB ? data.ProfitTB[item.dataIndex] : ''
          //   const valueHB = data.ProfitHB ? data.ProfitHB[item.dataIndex] : ''
          //   result += item.seriesName + ': ' + item.value
          //   if (valueTB) {
          //     result += ' (同比: ' + (valueTB * 100).toFixed(2) + '%)'
          //   }
          //   if (valueHB) {
          //     result += ' (环比: ' + (valueHB * 100).toFixed(2) + '%)'
          //   }
          //   result += '<br/>'
          // }
        })
        return result
      },
    },
    legend: {
      data: [obj.barName, obj.lineName],
      bottom: isMobile ? 0 : 'auto',
    },
    xAxis: [
      {
        type: 'category',
        data: data.xAxis,
        axisPointer: {
          type: 'shadow',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '', // names.barName,
        position: 'left',
        axisLabel: {
          formatter: '{value} ', // 元
        },
      },
      {
        type: 'value',
        name: '', // names.lineName,
        position: 'right',
        axisLabel: {
          formatter: '{value}%',
        },
      },
    ],
    series: [
      {
        name: obj.barName,
        type: 'bar',
        data: data.barData,
        itemStyle: {
          color: '#3F92FF',
        },
      },
      {
        name: obj.lineName,
        type: 'line',
        yAxisIndex: 1,
        data: data.lineData,
        smooth: true,
        itemStyle: {
          color: '#FFCB3F',
        },
        lineStyle: {
          width: 3,
        },
      },
    ],
  }

  return {
    chart: chartConfig,
    status: true,
  }
}

export const createRadarChart = (data) => {
  // 创建图表配置对象
  let chartConfig = {
    legend: {
      data: data.series.map((s) => s.name),
      left: 'left',
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(44, 44, 46, 0.9)',
      borderColor: '#48484a',
      textStyle: { color: '#ffffff' },
      padding: 10,
    },
    radar: {
      indicator: data.indicators,
      center: ['50%', '54%'],
      axisName: { color: '#CCCCCC', fontSize: 11 },
      splitArea: { show: false },
      splitLine: { lineStyle: { color: '#48484a' } },
      axisLine: { lineStyle: { color: '#48484a' } },
    },
    series: [
      {
        type: 'radar',
        areaStyle: {},
        data: data.series.map((s) => {
          s['areaStyle'] = {
            color: s.name === '当前门店' ? '#5B42D9' : '#22C55E',
            opacity: 0.5,
          }
          s['itemStyle'] = {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: s.name === '当前门店' ? '#5B42D9' : '#22C55E' }, // 起始色
                { offset: 1, color: s.name === '当前门店' ? '#2900A6' : '#006E3D' }, // 结束色
              ],
            },
          }
          return s
        }),
      },
    ],
  }

  return {
    chart: chartConfig,
    status: true,
  }
}
