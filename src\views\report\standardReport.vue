<script setup>
import { cancelAllRequests } from '@/network/axios'
import { CaretBottom, Sort } from '@element-plus/icons-vue'
import { ref, reactive, onMounted, onBeforeUnmount, nextTick, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useReportStore } from '@/stores/reportStore'
import {
  getRptData,
  getShopTree,
  getCategoryTree,
  getGoodsSelect,
  getVenderSelect,
  tableDownLoadToExcel,
  reportInterpret,
} from '@/api/reportApi'
import { createLineChart } from '@/echarts/echarts_Js/echartsJs'
import { filterStandardTheHeader, downLoad } from '@/tool/tool'

const route = useRoute()
const router = useRouter()

// 保存初始参数状态
const initialParams = ref('')
const params = ref('')
const RowId = ref('')
const vtype = ref('1')
const ColName = ref('')
// 设定初始kpi值为：实时销售
const KPIID = ref('1')
const Shops = ref('')
const Cates = ref('')
// 保存初始xAPPListParam状态
const initialXAPPListParam = ref(null)
const xAPPListParam = ref(null)

const xAPPListTplctr = ref(null)
const xAPPDic = reactive({})
// 保存初始compTypeGroup状态
const initialCompTypeGroup = ref(null)
const compTypeGroup = reactive({
  show: false,
  defaultV: '',
  value: '',
  paramChr: '',
  ParamName: '类型',
  list: [],
})

const filterBoxVisible = ref(false)
const toggleFilterBox = () => {
  resetFilter()
  filterBoxVisible.value = !filterBoxVisible.value
}

const hasExtraFilterItems = computed(() => {
  if (!xAPPListParam.value) return false

  // 检查是否有除了 @Shops 和 @Cates 之外的且 show 为 true 的项
  const hasExtraParams = xAPPListParam.value.some(
    (item) => item.paramChr !== '@Shops' && item.paramChr !== '@Cates' && item.show,
  )

  // 检查 compTypeGroup 是否显示
  const hasCompTypeGroup = compTypeGroup.show

  // 检查 quickFilterList 是否存在且不为空
  const hasQuickFilters = quickFilterList.length > 0

  // 任一条件满足即显示筛选按钮
  return hasExtraParams || hasCompTypeGroup || hasQuickFilters
})

// 创建响应式的 echartsData
const echartsData = reactive({})

// 从 Pinia 或 sessionStorage 获取的报表数据
const reportConfigurationData = ref(null)

// 添加防抖函数
const debounce = (fn, delay) => {
  let timer = null
  return function (...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

// 添加 tableHeight 计算
const tableHeight = computed(() => {
  const navigationHeight = navigationList.length ? 50 : 0
  const paginationHeight = 50
  return `calc(100vh - ${navigationHeight + paginationHeight + 200}px)` // 200px是其他固定元素的高度
})

const getReportData = () => {
  xAPPDic.value = reportConfigurationData.value.xAPPDic
  document.title = xAPPDic.value.appName || '标准报表'
  reportConfigurationData.value.xAPPListParam.map((item) => {
    if (params.value.indexOf(item.paramChr) < 0 && item.valueType * 1 !== 0) {
      if (
        item.paramChr == '@SDate1' ||
        item.paramChr == '@SDate2' ||
        item.paramChr == '@SDate3' ||
        item.paramChr == '@SDate4' ||
        item.paramChr == '@SMonth1' ||
        item.paramChr == '@SMonth2' ||
        item.paramChr == '@SWeek2' ||
        item.paramChr == '@SYear2'
      ) {
        params.value += `,${item.paramChr}=[${item.DefaultV}]`
      } else {
        params.value += `,${item.paramChr}=[]`
      }
    }
  })

  // 先清空quickFilterList，防止重复添加
  quickFilterList.length = 0

  const newList = reportConfigurationData.value.xAPPListParam.map((item) => {
    const newItem = { ...item }
    if (newItem.paramChr === '@Shops' && (newItem.ParamID * 1 === 6 || newItem.ParamID * 1 === 7)) {
      if (newItem.ParamName === '门店列表') {
        newItem.show = true
        newItem.value = ''
      }
    } else if (newItem.paramChr === '@Cates') {
      if (newItem.ParamName === '类别（控件）') {
        newItem.show = true
        newItem.value = ''
      }
    } else if (newItem.paramChr === '@CompType') {
      // 将@CompType的数据添加到组中
      compTypeGroup.list.push({
        value: newItem.DefaultV,
        text: newItem.ParamName,
      })
      if (newItem.Is_Select === 1) {
        compTypeGroup.value = newItem.ParamName
        compTypeGroup.defaultV = [newItem.DefaultV]

        const paramPattern = new RegExp(`${newItem.paramChr}=\\[.*?\\]`, 'g')
        const newValue = newItem.DefaultV

        params.value = params.value.replace(paramPattern, `${newItem.paramChr}=[${newValue}]`)
      }
      compTypeGroup.show = true
      compTypeGroup.paramChr = '@CompType'

      newItem.show = false // 隐藏原始项
    } else if (newItem.paramChr === '@Venders' && newItem.Is_Widget * 1 !== 0) {
      newItem.show = true
      newItem.value = newItem.Is_Select === 1 ? newItem.DefaultV : ''
    } else if (newItem.paramChr == '@Goods' && newItem.Is_Widget * 1 != 0) {
      newItem.show = true // 商品搜索插件
      if (newItem.Is_Select * 1 === 1) {
        newItem.value = newItem.DefaultV
        const paramPattern = new RegExp(`${newItem.paramChr}=\\[.*?\\]`, 'g')
        const newValue = newItem.DefaultV
        params.value = params.value.replace(paramPattern, `${newItem.paramChr}=[${newValue}]`)
      } else {
        newItem.value = ''
      }
    } else if (
      [
        '@SDate1',
        '@SDate2',
        '@SDate3',
        '@SDate4',
        '@SYear1',
        '@SYear2',
        '@SYear3',
        '@SYear4',
        '@SMonth1',
        '@SMonth2',
        '@SMonth3',
        '@SMonth4',
        '@SWeek1',
        '@SWeek2',
        '@SWeek3',
        '@SWeek4',
      ].includes(newItem.paramChr) &&
      newItem.Is_Widget !== 0
    ) {
      newItem.show = true
    } else if (newItem.Is_Widget * 1 === 1) {
      quickFilterList.push(newItem)

      if (newItem.Is_Select === 1) {
        quickFilterIndex.value = quickFilterList.length - 1
      }
    }
    return newItem
  })
  xAPPListParam.value = newList

  // 保存初始参数状态
  initialParams.value = params.value

  // 保存初始xAPPListParam状态（深拷贝）
  initialXAPPListParam.value = JSON.parse(JSON.stringify(xAPPListParam.value))

  // 保存初始compTypeGroup状态（深拷贝）
  initialCompTypeGroup.value = JSON.parse(JSON.stringify(compTypeGroup))

  initialQuickFilterList.value = JSON.parse(JSON.stringify(quickFilterList))
  initialQuickFilterIndex.value = quickFilterIndex.value

  if (
    reportConfigurationData.value.xAPPListTplctr &&
    reportConfigurationData.value.xAPPListTplctr.length
  ) {
    xAPPListTplctr.value = reportConfigurationData.value.xAPPListTplctr
  }
  if (xAPPDic.value.askKey) {
    getData(xAPPDic.value.askKey, 1)
    getData(xAPPDic.value.askKey, 2)
    getData(xAPPDic.value.askKey, 3)
  }
}

// 获取页面配置

const reportStatementList = reactive({
  KPIName: null,
  KPIValue: null,
  index: 0,
  list: [],
})
const lineEchartsRef = ref(null)
const tableRef = ref(null)

const tableData = reactive({
  columns: [],
  data: [],
  index: 0,
})

const getData = async (askKey, num) => {
  let data = {
    askKey: askKey,
    paramChr: `@UserID=[${sessionStorage.getItem('userId')}],@RowID=[${RowId.value}],@vtype=[${vtype.value}],@ColName=[${ColName.value}],@KPIID=[${KPIID.value}],@PartID=[${num}]${params.value}`,
  }
  console.log(data.paramChr, 'paramChr')
  if (
    askKey === '28F163C9BE3148EC8DEEA54674830E4B' &&
    num === 3 &&
    data.paramChr.includes('@Goods=[]')
  ) {
    return
  }
  try {
    const res = await getRptData(data)
    console.log('getData-res', res)
    if (num === 1) {
      reportStatementList.list = res
      reportStatementList.KPIName = reportStatementList.list[0].KPIName
      reportStatementList.KPIValue = reportStatementList.list[0].KPIValue
      KPIID.value = reportStatementList.list[0].KPIID
    }
    if (num === 2) {
      if (lineEchartsRef.value) {
        const chartConfig = createLineChart(res)
        Object.assign(echartsData, chartConfig.chart)
      }
    }
    if (num === 3) {
      let header = JSON.parse(JSON.stringify(res))
      let data = JSON.parse(JSON.stringify(res))
      tableData.columns = filterStandardTheHeader(header[0])

      tableData.data = data.slice(1)
      tableData.columns = tableData.columns.map((col) => {
        let maxLen = col.title.length
        tableData.data.forEach((row) => {
          const cell = row[col.key]
          if (cell && cell.toString().length > maxLen) {
            maxLen = cell.toString().length
          }
        })
        // 限制最小宽度100px，最大宽度300px
        const minWidth = Math.min(Math.max(maxLen * 15 + 30, 100), 300) + 'px'
        const leftMinWidth = Math.min(Math.max((maxLen / 3) * 15 + 30, 100), 150) + 'px'
        return {
          ...col,
          minWidth,
          leftMinWidth,
        }
      })
    }
  } catch (error) {
    console.error('获取页面配置失败:', error)
  }
  nextTick(() => {
    console.log('tableRef.value:', tableRef.value)
    console.log('tableRef.value type:', typeof tableRef.value)
    if (tableRef.value) {
      console.log('tableRef.value methods:', Object.getOwnPropertyNames(tableRef.value))
      console.log('setScrollLeft type:', typeof tableRef.value.setScrollLeft)
      console.log('setScrollTop type:', typeof tableRef.value.setScrollTop)
    }

    if (tableRef.value) {
      // 尝试使用 setScrollLeft 和 setScrollTop
      if (typeof tableRef.value.setScrollLeft === 'function') {
        tableRef.value.setScrollLeft(0)
        tableRef.value.setScrollTop(0)
        console.log('scroll to top and left using setScrollLeft/setScrollTop')
      }
      // 备用方案：使用 scrollTo 方法
      else if (typeof tableRef.value.scrollTo === 'function') {
        tableRef.value.scrollTo({ left: 0, top: 0 })
        console.log('scroll to top and left using scrollTo')
      }
      // 最后的备用方案：直接操作 DOM
      else {
        const tableWrapper = tableRef.value.$el?.querySelector('.el-table__body-wrapper')
        if (tableWrapper) {
          tableWrapper.scrollLeft = 0
          tableWrapper.scrollTop = 0
          console.log('scroll to top and left using DOM manipulation')
        } else {
          console.warn('No suitable scroll method found')
        }
      }
    } else {
      console.warn('tableRef is not available')
    }
  })
}

// 处理表格样式和功能
const handleTableData = (key, row) => {
  const value = row[key]
  const valStr = value?.toString() ?? ''
  // 1. a_0 特殊样式
  if (row.a_0 !== '' && row.a_0 !== undefined && row.a_0 !== 'undefined' && row.a_0 !== null) {
    if (key === 'a_2') {
      return {
        color: '#2d8cf0',
        cursor: 'pointer',
      }
    }
  }
  // 2. d_ 开头且包含负号（-）
  if (key.startsWith('d_') && valStr.includes('-')) {
    return {
      color: '#ed4014',
    }
  }
  // 处理数字判断
  // const num = parseFloat(value?.toString().replace(/[^0-9.-]/g, ''))
  // if (!isNaN(num) && num < 1) {
  //   return {
  //     color: 'red',
  //   }
  // }
  return {}
}

//获取门店Tree数据
const getShopData = async () => {
  let data = {
    head: {
      accessToken: sessionStorage.getItem('token'),
    },
  }
  const res = await getShopTree(data)

  shopsTreeData.value = res
}

//获取类别Tree数据
const getCategoryData = async () => {
  let data = {
    head: {
      accessToken: sessionStorage.getItem('token'),
    },
  }
  const res = await getCategoryTree(data)

  CatesTreeData.value = res
}

const customFieldName = {
  text: '',
  value: '',
}
const goodsData = ref([])
const getGoodsData = async (data, item) => {
  const res = await getGoodsSelect(data)

  goodsData.value = JSON.parse(JSON.stringify(res))
  customFieldName.text = 'goodsName'
  customFieldName.value = 'goodsId'

  controlsStatus.value = item.paramChr
  show.value = true
}

const venderData = ref([])
const getVenderData = async (data, item) => {
  const res = await getVenderSelect(data)

  venderData.value = JSON.parse(JSON.stringify(res))
  customFieldName.text = 'venderName'
  customFieldName.value = 'venderId'

  controlsStatus.value = item.paramChr
  show.value = true
}

// 控件状态
const controlsStatus = ref(null)
// 弹窗展示
const show = ref(false)

const keyUpControls = debounce((item) => {
  // 如果输入框为空，说明是删除操作
  if (item.value === '' || item.value === null || item.value === undefined) {
    return
  }
  let data = {
    pageNo: 1,
    pageSize: 100,
    searchParam: item.value,
  }

  if (item.paramChr === '@Goods') {
    getGoodsData(data, item)
  }
  if (item.paramChr === '@Venders') {
    getVenderData(data, item)
  }
}, 1000)

const indicatorNavigationAllStatus = ref(null)

const selectControls = (item) => {
  if (item.paramChr == 'indicatorNavigationAll') {
    indicatorNavigationAllStatus.value = 'indicatorNavigationAll'
    show.value = true
    controlsStatus.value = null
  } else {
    controlsStatus.value = item.paramChr
    if (item.paramChr == '@Shops') {
      show.value = true
    }

    if (item.paramChr == '@Cates') {
      show.value = true
    }

    if (item.paramChr == '@CompType') {
      show.value = true
    }
    indicatorNavigationAllStatus.value = null
  }
}

// 树形控件页面展示
const treeName = ref('')
// 树形控件展示（必须，但不展示）
const treeValue = ref()
//门店列表
const shopsTreeData = ref([])
//类别列表
const CatesTreeData = ref([])
//树形控件默认参数
const defaultProps = ref({
  children: 'children',
  label: 'name',
})
//树形控件选择操作
const treeCheckChange = (checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys) => {
  // 创建一个数组来存储所有节点的id
  const allIds = checkedKeys.checkedNodes.map((node) => node.id)

  // 过滤出符合条件的节点
  const arr = checkedKeys.checkedNodes.filter((node) => {
    // 如果节点的pid不在allIds数组中，说明这个节点没有被其他节点引用
    return !allIds.includes(node.pid)
  })

  if (arr.length > 0) {
    // 将arr中的shopId拼接成字符串
    treeName.value = arr.map((node) => node.name).join(',')
    if (controlsStatus.value === '@Shops') {
      Shops.value = arr.map((node) => node.shopId).join(',')
      const paramPattern = new RegExp(`@Shops=\\[.*?\\]`, 'g')
      params.value = params.value.replace(paramPattern, `@Shops=[${Shops.value}]`)
    }
    if (controlsStatus.value === '@Cates') {
      Cates.value = arr.map((node) => node.id).join(',')

      const paramPattern = new RegExp(`@Cates=\\[.*?\\]`, 'g')

      params.value = params.value.replace(paramPattern, `@Cates=[${Cates.value}]`)
    }
  }
}
//时间控件页面展示
const currentDate = ref([])
const confirm = (value) => {
  show.value = false

  // 查找并替换 params.value 中的值
  if (params.value && controlsStatus.value) {
    // 日期选择器确认
    if (controlsStatus.value === '@SDate1' || controlsStatus.value === '@SDate2') {
      const paramPattern = new RegExp(`${controlsStatus.value}=\\[.*?\\]`, 'g')
      const newValue = currentDate.value.join('-')
      params.value = params.value.replace(paramPattern, `${controlsStatus.value}=[${newValue}]`)

      // 更新对应字段的value
      if (xAPPListParam.value) {
        const targetItem = xAPPListParam.value.find(
          (item) => item.paramChr === controlsStatus.value,
        )
        if (targetItem) {
          targetItem.value = newValue
        }
      }
    } else if (controlsStatus.value === '@SYear2') {
      const paramPattern = new RegExp(`${controlsStatus.value}=\\[.*?\\]`, 'g')
      const newValue = currentDate.value.join('-')
      params.value = params.value.replace(paramPattern, `${controlsStatus.value}=[${newValue}]`)
      // 更新对应字段的value
      if (xAPPListParam.value) {
        const targetItem = xAPPListParam.value.find(
          (item) => item.paramChr === controlsStatus.value,
        )
        if (targetItem) {
          targetItem.value = newValue
        }
      }
    } else if (controlsStatus.value === '@Shops' || controlsStatus.value === '@Cates') {
      for (let i = 0; i < xAPPListParam.value.length; i++) {
        if (controlsStatus.value === '@Shops') {
          if (xAPPListParam.value[i].paramChr === '@Shops') {
            xAPPListParam.value[i].value = treeName.value
          }
        }
        if (controlsStatus.value === '@Cates') {
          if (xAPPListParam.value[i].paramChr === '@Cates') {
            xAPPListParam.value[i].value = treeName.value
          }
        }
      }
    } else if (controlsStatus.value === '@CompType') {
      compTypeGroup.value = value.selectedOptions[0].text
      const paramPattern = new RegExp(`${controlsStatus.value}=\\[.*?\\]`, 'g')
      const newValue = value.selectedOptions[0].value
      params.value = params.value.replace(paramPattern, `${controlsStatus.value}=[${newValue}]`)
    } else if (controlsStatus.value === '@Venders') {
      const paramPattern = new RegExp(`${controlsStatus.value}=\\[.*?\\]`, 'g')
      const newValue = value.selectedOptions[0].venderId
      params.value = params.value.replace(paramPattern, `${controlsStatus.value}=[${newValue}]`)
      if (xAPPListParam.value) {
        const targetItem = xAPPListParam.value.find(
          (item) => item.paramChr === controlsStatus.value,
        )
        if (targetItem) {
          targetItem.value = value.selectedOptions[0].venderName
        }
      }
    } else if (controlsStatus.value === '@Goods') {
      const paramPattern = new RegExp(`${controlsStatus.value}=\\[.*?\\]`, 'g')
      const newValue = value.selectedOptions[0].goodsId
      params.value = params.value.replace(paramPattern, `${controlsStatus.value}=[${newValue}]`)
      if (xAPPListParam.value) {
        const targetItem = xAPPListParam.value.find(
          (item) => item.paramChr === controlsStatus.value,
        )
        if (targetItem) {
          targetItem.value = value.selectedOptions[0].goodsName
        }
      }
    } else if (controlsStatus.value === '@int') {
      const paramPattern = new RegExp(`${controlsStatus.value}=\\[.*?\\]`, 'g')
      const newValue = value.selectedOptions[0].value
      params.value = params.value.replace(paramPattern, `${controlsStatus.value}=[${newValue}]`)
    }
  }
  if (filterBoxVisible.value === false) {
    getData(xAPPDic.value.askKey, 1)
    getData(xAPPDic.value.askKey, 2)
    getData(xAPPDic.value.askKey, 3)
  }
}

const resetFilter = () => {
  // 恢复初始参数
  params.value = initialParams.value

  // 重置 xAPPListParam
  if (initialXAPPListParam.value) {
    xAPPListParam.value.forEach((item, index) => {
      if (item.show) {
        // 从初始状态恢复每个控件的值
        const initialItem = initialXAPPListParam.value.find((i) => i.paramChr === item.paramChr)
        if (initialItem) {
          item.value = initialItem.value
        }
      }
    })
  }

  // 重置 compTypeGroup
  if (initialCompTypeGroup.value) {
    compTypeGroup.value = initialCompTypeGroup.value.value
    compTypeGroup.ParamName = initialCompTypeGroup.value.ParamName
    compTypeGroup.defaultV = initialCompTypeGroup.value.defaultV
  }

  // 重置 quickFilterList
  if (initialQuickFilterList.value.length > 0) {
    // 清空当前列表
    quickFilterList.length = 0

    // 从初始状态恢复
    initialQuickFilterList.value.forEach((item) => {
      quickFilterList.push(JSON.parse(JSON.stringify(item)))
    })

    // 恢复选中索引
    quickFilterIndex.value = initialQuickFilterIndex.value
  }
  RowId.value = ''
  vtype.value = 1
  ColName.value = ''
  // KPIID.value = ''

  navigationList.length = 0

  console.log('所有参数已重置为初始状态', params.value)
}

const searchFilter = () => {
  getData(xAPPDic.value.askKey, 1)
  getData(xAPPDic.value.askKey, 2)
  getData(xAPPDic.value.askKey, 3)
  filterBoxVisible.value = false
}

// 快速筛选
const quickFilterList = reactive([])
const quickFilterIndex = ref(0)
const initialQuickFilterList = ref([])
const initialQuickFilterIndex = ref(0)
const quickFilterChange = (item, index) => {
  quickFilterIndex.value = index
  if (filterBoxVisible.value === false) {
    getData(xAPPDic.value.askKey)
  }
}

const changeReportStatementList = (item, index) => {
  reportStatementList.KPIName = item.KPIName
  reportStatementList.KPIValue = item.KPIValue
  reportStatementList.index = index
  KPIID.value = item.KPIID
  getData(xAPPDic.value.askKey, 2)
  getData(xAPPDic.value.askKey, 3)
  show.value = false
  // 添加滚动功能
  nextTick(() => {
    const tabsBox = document.querySelector('.tabs-box')
    const tabItem = document.querySelectorAll('.tab-item-box')[index]
    if (tabsBox && tabItem) {
      tabsBox.scrollTo({
        left: tabItem.offsetLeft - tabsBox.offsetLeft,
        behavior: 'smooth',
      })
    }
  })
}

const tableSwitch = reactive({
  index: 1,
  list: [
    { type: 'primary', label: '门店', vtype: 2 },
    { type: 'primary', label: '类别', vtype: 1 },
  ],
})
// 表格切换
const tableSwitchChange = (item, index) => {
  tableSwitch.index = index
  vtype.value = item.vtype

  getData(xAPPDic.value.askKey, 3)
}

// 下载表格Excel
const tableDownLoad = async () => {
  let data = {
    askKey: xAPPDic.value.askKey,
    isNew: '',
    paramChr: `@UserID=[${sessionStorage.getItem('userId')}],@RowID=[${RowId.value}],@vtype=[${vtype.value}],@ColName=[${ColName.value}],@KPIID=[${KPIID.value}],@PartID=[3]${params.value}`,
    title: xAPPDic.value.appName,
  }
  // let data = {
  //   head: {
  //     accessToken: sessionStorage.getItem('token'),
  //   },
  //   body: {
  //     askKey: xAPPDic.value.askKey,
  //     title: xAPPDic.value.appName,
  //     isNew: '',
  //     paramChr: `@UserID=[${sessionStorage.getItem('userId')}],@RowID=[${RowId.value}],@vtype=[${vtype.value}],@ColName=[${ColName.value}],@KPIID=[${KPIID.value}],@PartID=[3]${params.value}`,
  //   },
  // }
  const res = await tableDownLoadToExcel(data)
  downLoad(res, `${xAPPDic.value.appName}.xlsx`)
}

//下钻导航
const navigationList = reactive([])
// 删除navigationList对应下标
const navigationChange = (item, index, str) => {
  if (index === navigationList.length - 1) {
    return
  }

  if (item.a_2 === '回到首页') {
    navigationList.length = 0
  } else {
    navigationList.splice(index + 1)
  }

  handleTableClick(item, str)
}

//表格下钻
const handleTableClick = (row, str) => {
  if (str === 'table') {
    if (row.a_0 !== '' && row.a_0 !== undefined && row.a_0 !== 'undefined' && row.a_0 !== null) {
      RowId.value = row.a_1
      ColName.value = row.a_0

      // 创建深拷贝对象
      const homeRow = JSON.parse(JSON.stringify(row))
      // 清空所有值
      Object.keys(homeRow).forEach((key) => {
        homeRow[key] = ''
      })
      // 设置a_2为"回到首页"
      homeRow.a_2 = '回到首页'

      // 检查navigationList是否为空，如果为空则添加homeRow
      if (navigationList.length === 0) {
        navigationList.push(homeRow)
      }

      // 添加当前行到navigationList
      navigationList.push(row)

      // 处理paramChr
      if (row.a_0.includes('SName') || row.a_0.includes('ShopName')) {
        const paramPattern = new RegExp(`@Shops=\\[.*?\\]`, 'g')
        params.value = params.value.replace(paramPattern, `@Shops=[${row.a_1}]`)
        if (vtype.value === 2) {
          vtype.value = 1
          tableSwitch.index = 1
        }
      }
      if (row.a_0.includes('CategoryName')) {
        const paramPattern = new RegExp(`@Cates=\\[.*?\\]`, 'g')
        params.value = params.value.replace(paramPattern, `@Cates=[${row.a_1}]`)
      }
      if (row.a_0.includes('GoodsName')) {
        // 检查参数中是否包含@Goods或@Shops
        if (params.value.includes('@Goods')) {
          const paramPattern = new RegExp(`@Goods=\\[.*?\\]`, 'g')
          params.value = params.value.replace(paramPattern, `@Goods=[${row.a_1}]`)
        }
        // if (params.value.includes('@Shops')) {
        //   const paramPattern = new RegExp(`@Shops=\\[.*?\\]`, 'g')
        //   params.value = params.value.replace(paramPattern, `@Shops=[${Shops.value}]`)
        // }
      }

      // if (quickFilterList[quickFilterIndex.value]?.DefaultV) {
      //   const [startDate, endDate] = quickFilterList[quickFilterIndex.value].DefaultV.split(',')

      //   if (params.value.includes('@SDate1')) {
      //     const paramPattern = new RegExp(`@SDate1=\\[.*?\\]`, 'g')
      //     params.value = params.value.replace(paramPattern, `@SDate1=[${startDate}]`)
      //   }
      //   if (params.value.includes('@SDate2')) {
      //     const paramPattern = new RegExp(`@SDate2=\\[.*?\\]`, 'g')
      //     params.value = params.value.replace(paramPattern, `@SDate2=[${endDate}]`)
      //   }
      // }
    } else {
      return
    }
  }
  if (str === 'navigationChange') {
    RowId.value = row.a_1
    ColName.value = row.a_2 === '回到首页' ? '' : row.a_0
    if (row.a_2 === '回到首页') {
      if (params.value.includes('@Shops')) {
        const paramPattern = new RegExp(`@Shops=\\[.*?\\]`, 'g')
        params.value = params.value.replace(paramPattern, `@Shops=[${Shops.value}]`)
      }
      if (params.value.includes('@Cates')) {
        const paramPattern = new RegExp(`@Cates=\\[.*?\\]`, 'g')
        params.value = params.value.replace(paramPattern, `@Cates=[${Cates.value}]`)
      }
      vtype.value = 1
      tableSwitch.index = 1
    }
    if (row.a_0.includes('SName') || row.a_0.includes('ShopName')) {
      let paramPattern = new RegExp(`@Shops=\\[.*?\\]`, 'g')
      params.value = params.value.replace(paramPattern, `@Shops=[${row.a_1}]`)

      paramPattern = new RegExp(`@Cates=\\[.*?\\]`, 'g')
      params.value = params.value.replace(paramPattern, `@Cates=[]`)
    }
    if (row.a_0.includes('CategoryName')) {
      const paramPattern = new RegExp(`@Cates=\\[.*?\\]`, 'g')
      params.value = params.value.replace(paramPattern, `@Cates=[${row.a_1}]`)
    }
    if (row.a_0.includes('GoodsName')) {
      // 检查参数中是否包含@Goods或@Shops
      if (params.value.includes('@Goods')) {
        const paramPattern = new RegExp(`@Goods=\\[.*?\\]`, 'g')
        params.value = params.value.replace(paramPattern, `@Goods=[${row.a_1}]`)
      }
      if (params.value.includes('@Shops')) {
        const paramPattern = new RegExp(`@Shops=\\[.*?\\]`, 'g')
        params.value = params.value.replace(paramPattern, `@Shops=[${row.a_1}]`)
      }
    }

    if (quickFilterList[quickFilterIndex.value]?.DefaultV) {
      const [startDate, endDate] = quickFilterList[quickFilterIndex.value].DefaultV.split(',')

      if (params.value.includes('@SDate1')) {
        const paramPattern = new RegExp(`@SDate1=\\[.*?\\]`, 'g')
        params.value = params.value.replace(paramPattern, `@SDate1=[${startDate}]`)
      }
      if (params.value.includes('@SDate2')) {
        const paramPattern = new RegExp(`@SDate2=\\[.*?\\]`, 'g')
        params.value = params.value.replace(paramPattern, `@SDate2=[${endDate}]`)
      }
    }
  }

  getData(xAPPDic.value.askKey, 1)
  getData(xAPPDic.value.askKey, 2)
  getData(xAPPDic.value.askKey, 3)
}

const getReportInterpret = async () => {
  let data = {
    askKey: xAPPDic.value.askKey,
    paramChr: `@UserID=[${sessionStorage.getItem('userId')}],@RowID=[${RowId.value}],@vtype=[${vtype.value}],@ColName=[${ColName.value}],@KPIID=[${KPIID.value}],@PartID=[3]${params.value}`,
    title: xAPPDic.value.appName,
    isNew: '',
  }
  console.log(data.paramChr, 'paramChr')
  try {
    const res = await reportInterpret(data)
    console.log('reportInterpret', res)
    sessionStorage.setItem('reportInfo', JSON.stringify({ name: res.title, url: res.ossFilePath }))
    router.push({
      path: '/ai',
      query: {
        t: sessionStorage.getItem('token'),
        u: sessionStorage.getItem('userId'),
        a: 'report',
      },
    })
  } catch (error) {
    console.error('报表解读失败', error)
  }
}

onMounted(() => {
  // 初始化 Pinia store
  const reportStore = useReportStore()

  // 从 Pinia 或 sessionStorage 获取报表数据
  if (reportStore.reportConfigurationData) {
    reportConfigurationData.value = reportStore.reportConfigurationData
  } else {
    // 如果 Pinia 中没有数据，尝试从 sessionStorage 获取
    const sessionData = sessionStorage.getItem('reportConfigurationData')
    if (sessionData) {
      reportConfigurationData.value = JSON.parse(sessionData)
    }
  }

  getReportData()
  getShopData()
  getCategoryData()
})
onBeforeUnmount(() => {
  cancelAllRequests()
})
</script>

<template>
  <div class="standard-report">
    <div class="report-interpret" @click="getReportInterpret">
      <!-- <img alt src="@/assets/images/report/ai.png" /> -->
    </div>
    <!-- <div class="report-header">
      <h2 v-if="JSON.stringify(xAPPDic) !== '{}'">标准报表{{ xAPPDic.value.appName }}</h2>
    </div> -->
    <div class="report-content">
      <div v-if="route.query.data">
        <h3>报表信息</h3>
        <div class="info-item">
          <span>名称：</span>
          <span>{{ JSON.parse(route.query.data).item.name }}</span>
        </div>
      </div>
      <div class="module-box module-box-first">
        <!-- <h3>控件</h3> -->
        <div class="controls-box">
          <div v-if="xAPPListParam" class="controls-item">
            <div
              v-for="(item, index) in xAPPListParam.filter(
                (item) => (item.paramChr === '@Shops' || item.paramChr === '@Cates') && item.show,
              )"
              :key="index"
            >
              <div v-if="item.show" class="controls-item-box">
                <el-input
                  v-model="item.value"
                  :placeholder="item.value ? item.value : item.ParamName"
                  :readonly="item.paramChr !== '@Goods' && item.paramChr !== '@Venders'"
                  :suffix-icon="
                    item.paramChr !== '@Goods' && item.paramChr !== '@Venders' ? Sort : ''
                  "
                  @keyup="keyUpControls(item)"
                  @click="selectControls(item)"
                />
                <!-- <van-field
                class="controls-item-field"
                v-model="item.value"
                readonly
                :label="item.ParamName"
                placeholder="请选择"
                @click="selectControls(item)"
              /> -->
              </div>
            </div>
            <!--<div v-if="compTypeGroup.show" class="controls-item">
               <el-input
                v-model="compTypeGroup.value"
                :placeholder="compTypeGroup.value ? compTypeGroup.value : compTypeGroup.ParamName"
                readonly
                :suffix-icon="CaretBottom"
                @click="selectControls(compTypeGroup)"
              />
              <van-field
              class="controls-item-field"
              v-model="compTypeGroup.value"
              readonly
              :label="compTypeGroup.ParamName"
              placeholder="请选择类型"
              @click="selectControls(compTypeGroup)"
            />
            </div>-->
          </div>
          <el-button v-if="hasExtraFilterItems" class="filter-button" @click="toggleFilterBox"
            ><img src="../../assets/images/report/filter.png" alt="筛选" />筛选
          </el-button>
          <!-- <van-button
            type="primary"
            size="small"
            v-if="hasExtraFilterItems"
            class="filter-button"
            @click="toggleFilterBox"
            >筛选</van-button
          > -->
        </div>
      </div>
      <div v-if="filterBoxVisible" class="filter-overlay" @click="filterBoxVisible = false"></div>
      <!-- <transition name="slide-fade"> -->
      <div class="filter-box" v-if="filterBoxVisible">
        <div class="filter-item">
          <div
            class="filter-item-box"
            v-for="(item, index) in xAPPListParam.filter(
              (item) => item.paramChr !== '@Shops' && item.paramChr !== '@Cates' && item.show,
            )"
            :key="index"
          >
            <div class="filter-item-box-item" v-if="item.show">
              <el-input
                v-model="item.value"
                :placeholder="item.value ? item.value : item.ParamName"
                :readonly="item.paramChr !== '@Goods' && item.paramChr !== '@Venders'"
                :suffix-icon="
                  item.paramChr !== '@Goods' && item.paramChr !== '@Venders' ? Sort : ''
                "
                @keyup="keyUpControls(item)"
                @click="selectControls(item)"
              />
            </div>
          </div>
          <div v-if="compTypeGroup.show" class="filter-item-box">
            <div class="filter-item-box-item">
              <el-input
                v-model="compTypeGroup.value"
                :placeholder="compTypeGroup.value ? compTypeGroup.value : compTypeGroup.ParamName"
                readonly
                :suffix-icon="Sort"
                @click="selectControls(compTypeGroup)"
              />
            </div>
          </div>
          <div class="quickFilter-box" v-if="quickFilterList.length">
            <van-button
              size="small"
              :type="quickFilterIndex === index ? 'primary' : 'default'"
              block
              v-for="(item, index) in quickFilterList"
              :key="index"
              @click="quickFilterChange(item, index)"
              >{{ item.ParamName }}
            </van-button>
          </div>
        </div>
        <div class="filter-button-box">
          <van-button size="small" @click="resetFilter">重置</van-button>
          <van-button type="primary" size="small" @click="searchFilter">确定</van-button>
        </div>
      </div>
      <!-- </transition> -->
      <!-- <div class="module-box">
        <h3>快速筛选</h3>
        <div class="quickFilter-box" v-if="quickFilterList.length">
          <van-button
            :type="quickFilterIndex === index ? 'primary' : 'default'"
            block
            v-for="(item, index) in quickFilterList"
            :key="index"
            @click="quickFilterChange(item, index)"
            >{{ item.ParamName }}
          </van-button>
        </div>
      </div> -->
      <div class="module-box module-box-change">
        <div class="indicatorNavigation">
          <div
            class="indicatorNavigation-all"
            @click="selectControls({ paramChr: 'indicatorNavigationAll' })"
          >
            全部指标
          </div>
          <div class="tabs-box">
            <div
              class="tab-item-box"
              :class="[reportStatementList.index === index ? 'tab-item-box-selected' : '']"
              v-for="(item, index) in reportStatementList.list"
              :key="item.vtype"
              :type="item.type"
              @click="changeReportStatementList(item, index)"
            >
              <div>{{ item.KPIName }}</div>
              <div>{{ item.KPIValue }}</div>
              <div>{{ item.SubKPIName }} {{ item.SubKPIValue }}</div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="xAPPListTplctr && xAPPListTplctr.length">
        <div v-for="(item, index) in xAPPListTplctr.slice(1)" :key="index">
          <div el-card class="card-box">
            <!-- <template #header> -->
            <div class="card-header">
              <div>
                {{
                  item.attrName === 'IsShowPart1'
                    ? '指标分析'
                    : item.attrName === 'IsShowPart2'
                      ? '趋势'
                      : xAPPDic.value.appName
                }}：{{ reportStatementList.KPIName }} {{ reportStatementList.KPIValue }}
              </div>
              <div v-if="item.attrName === 'IsShowPart3'">
                <div class="table-switch-tag-download">
                  <!-- <el-tag
                      class="table-switch-tag"
                      :effect="tableSwitch.index === index ? 'dark' : 'plain'"
                      v-for="(item, index) in tableSwitch.list"
                      :key="item.vtype"
                      :type="item.type"
                      @click="tableSwitchChange(item, index)"
                      >{{ item.label }}</el-tag
                    > -->
                  <!-- <img
                      alt
                      src="@/assets/images/download.png"
                      style="width: 20px; height: 20px"
                      @click="tableDownLoad"
                    /> -->
                </div>
              </div>
            </div>
            <!-- </template> -->
            <!-- <div class="xAPPListTplctr-box" v-if="item.attrName === 'IsShowPart1'">
              <div
                v-for="(item, index) in reportStatementList.list"
                :key="index"
                class="xAPPListTplctr-box-item"
                :class="[
                  reportStatementList.index === index ? 'xAPPListTplctr-box-item-selected' : '',
                ]"
                @click="changeReportStatementList(item, index)"
              >
                <span>{{ item.KPIName }}</span>
                <span>{{ item.KPIValue }}</span>
                <div>{{ item.SubKPIName }} {{ item.SubKPIValue }}</div>
              </div>
            </div> -->
            <div v-if="item.attrName === 'IsShowPart2'">
              <echartsDemo :echartsData="echartsData" ref="lineEchartsRef"></echartsDemo>
            </div>

            <div v-if="item.attrName === 'IsShowPart3'">
              <div class="navigation-box" v-if="navigationList.length">
                <p>
                  <span
                    v-for="(item, index) in navigationList"
                    :key="index"
                    @click="navigationChange(item, index, 'navigationChange')"
                    >{{ item.a_2 }}<van-icon name="arrow"
                  /></span>
                </p>
              </div>
              <p>
                <el-tag
                  class="table-switch-tag table-switch-tag-change"
                  :effect="tableSwitch.index === index ? 'dark' : 'plain'"
                  v-for="(item, index) in tableSwitch.list"
                  :key="item.vtype"
                  :type="item.type"
                  @click="tableSwitchChange(item, index)"
                  >{{ item.label }}
                </el-tag>
              </p>
              <el-table
                ref="tableRef"
                :data="tableData.data"
                style="width: 100%"
                border
                stripe
                :height="tableHeight"
              >
                <el-table-column
                  v-for="(item, index) in tableData.columns"
                  :key="item.key"
                  :prop="item.key"
                  :label="item.title"
                  :fixed="index === 0 ? 'left' : false"
                  :min-width="index === 0 ? item.leftMinWidth : item.minWidth"
                >
                  <template #default="{ row }">
                    <span
                      :class="index === 0 ? 'left-fixed-ellipsis' : 'default-fixed-ellipsis'"
                      :style="handleTableData(item.key, row)"
                      @click="handleTableClick(row, 'table')"
                      >{{ row[item.key] }}</span
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <van-popup
      v-model:show="show"
      :round="controlsStatus === '@SDate1' || controlsStatus === '@SDate2'"
      position="bottom"
    >
      <div v-if="indicatorNavigationAllStatus === 'indicatorNavigationAll'">
        <div class="operate-box-indicatorNavigationAll">
          <span>全部指标</span>
          <van-icon name="close" class="close-icon" @click="show = false" />
        </div>
        <div class="indicatorNavigationAll-box">
          <div
            class="indicatorNavigationAll-box-item"
            :class="[
              reportStatementList.index === index ? 'indicatorNavigationAll-box-item-selected' : '',
            ]"
            :title="item.KPIName"
            v-for="(item, index) in reportStatementList.list"
            :key="item.vtype"
            :type="item.type"
            @click="changeReportStatementList(item, index)"
          >
            <div>{{ item.KPIName }}</div>
            <div>{{ item.KPIValue }}</div>
            <div>{{ item.SubKPIName }} {{ item.SubKPIValue }}</div>
          </div>
        </div>
      </div>
      <div class="van-popup-box" v-if="controlsStatus === '@Shops' || controlsStatus === '@Cates'">
        <div class="operate-box">
          <span @click="show = false">取消</span>
          <span v-if="controlsStatus === '@Shops'">选择门店列表</span>
          <span v-if="controlsStatus === '@Cates'">选择类别列表</span>
          <span @click="confirm">确定</span>
        </div>
        <div class="van-popup-tree-box">
          <el-tree
            v-if="controlsStatus === '@Shops' && shopsTreeData.length"
            v-model="treeValue"
            :data="shopsTreeData"
            default-expand-all
            show-checkbox
            style="width: 240px"
            :props="defaultProps"
            @check="treeCheckChange"
          />
          <el-tree
            v-if="controlsStatus === '@Cates' && CatesTreeData.length"
            v-model="treeValue"
            :data="CatesTreeData"
            default-expand-all
            show-checkbox
            style="width: 240px"
            :props="defaultProps"
            @check="treeCheckChange"
          />
        </div>
      </div>
      <van-picker
        v-if="controlsStatus == '@Goods' || controlsStatus == '@Venders'"
        title="请选择"
        :columns="controlsStatus == '@Goods' ? goodsData : venderData"
        @cancel="show = false"
        @confirm="confirm"
        :columns-field-names="customFieldName"
        show-toolbar
      />
      <van-picker
        v-if="controlsStatus == '@CompType'"
        v-model="compTypeGroup.defaultV"
        title="请选择"
        :columns="compTypeGroup.list"
        @cancel="show = false"
        @confirm="confirm"
        show-toolbar
      />
    </van-popup>
  </div>
</template>

<style scoped lang="less">
.standard-report {
  padding: 20px;
  background: linear-gradient(180deg, #f8f9fc 0%, #f8faff 100%);

  .report-interpret {
    // font-size: 26px;
    // font-weight: bold;
    // color: #333;
    position: fixed;
    right: 0;
    top: 50%;
    // padding: 10px 15px;
    // border: 1px solid #e4e7ed;
    // background: #fff;
    z-index: 1000;
    // border-right: none;
    width: 112px;
    height: 112px;
    background-image: url('../../assets/images/report/ai.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;

    overflow: hidden;
    // img {
    //   display: block;
    //   width: 36px;
    //   height: 40px;
    //   margin: 0 auto 5px auto;
    // }
  }
  .report-header {
    margin-bottom: 20px;
    h2 {
      font-size: 24px;
      color: #333;
    }
  }

  .report-content {
    // background: #fff;
    // padding: 20px 0;
    // border-radius: 8px;
    // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    .info-item {
      margin: 10px 0;
      font-size: 16px;

      span:first-child {
        font-weight: bold;
        margin-right: 10px;
      }
    }
    .module-box {
      // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      // padding: 10px;
      // background: #fff;
      // margin: 0 0 15px 0;
      // border-radius: 8px;
      // border: 1px solid #e4e7ed;

      .controls-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .controls-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: nowrap;
          width: 85%;
          .controls-item-box {
            width: 95%;
          }
          :deep(.el-input) {
            // border: none;
            // width: 100%;
            // margin: 0 10px 0 0;
          }
          :deep(.el-input__wrapper) {
            background: #f0f0ff;
            border-radius: 30px;
          }
          :deep(.el-input__inner) {
            color: #6a5acd;
          }
          :deep(.el-input__inner::placeholder) {
            // color: #606266;
            color: #6a5acd;
          }
          :deep(.el-input__icon) {
            color: #6a5acd;
          }
          // .controls-item-field {
          //   width: 100%;
          // }
        }
        .filter-button {
          margin: 0 10px 0 0;
          border: 2px solid #5b42d9;
          color: #5b42d9;
          img {
            width: 24x;
            height: 24px;
            margin: 0 8px 0 0;
          }
        }
      }
      // .quickFilter-box {
      //   margin-top: 20px;
      //   padding-top: 20px;
      //   border-top: 1px solid #eee;
      //   display: flex;
      //   justify-content: flex-start;
      //   align-items: center;
      // }
      .indicatorNavigation {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .indicatorNavigation-all {
          height: 202px;
          font-size: 30px;
          font-weight: bold;
          margin: 0 10px 0 0;
          padding: 5px 15px;
          color: #fff;
          display: flex;
          justify-content: center;
          align-items: center;
          background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
          box-shadow: 0px 8px 24px 0px rgba(99, 102, 241, 0.3);
          border-radius: 32px;
        }
        .tabs-box {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          flex-wrap: nowrap;
          overflow: hidden;
          overflow-x: scroll;
          .tab-item-box {
            overflow: hidden;
            width: 299px;
            // height: 125px;
            height: 202px;
            margin: 0 24px 0 0;
            padding: 0 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #eee;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            flex-shrink: 0; /* 防止元素被压缩 */
            flex-basis: 250px; /* 设置基础宽度 */
            background: linear-gradient(180deg, #f7f5ff 0%, #ffffff 100%);
            border-radius: 16px;
            border: 2px solid rgba(91, 66, 217, 0.1);
            div:first-child {
              // margin: 5px 0;
              // padding: 1px;
              width: 100%;
              text-align: center;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              font-weight: 400;
              font-size: 26px;
              color: #666666;
              line-height: 39px;
              font-style: normal;
              text-transform: none;
            }
            div:nth-child(2) {
              width: 100%;
              text-align: center;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              font-weight: 600;
              font-size: 36px;
              color: #5b42d9;
              line-height: 54px;
              font-style: normal;
              text-transform: none;
            }
            div:last-child {
              height: 41px;
              line-height: 41px;
              background: rgba(91, 66, 217, 0.08);
              border-radius: 20px;
              // margin: 5px 0;
              // padding: 1px;
              width: 100%;
              text-align: center;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              font-weight: 400;
              font-size: 22px;
              color: #f56c6c;

              font-style: normal;
              text-transform: none;
            }
          }
          .tab-item-box-selected {
            overflow: hidden;
            background: #1576ff;
            div {
              color: #fff !important;
            }
          }
        }
      }
    }
    .module-box-first {
      position: relative;
      z-index: 101;
    }
    .module-box-change {
      margin: 15px 0 28px 0;
      padding: 24px 6px;
      background: #ffffff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      border-radius: 24px;
    }
    .filter-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 100;
    }

    .filter-box {
      position: absolute;
      top: 151px;
      left: 20px;
      right: 20px;
      background: #fff;
      padding: 15px;
      // border-radius: 4px;
      // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
      z-index: 101;
      transform-origin: top center;
      animation: dropdown 0.3s ease-out;
      overflow: hidden;
      .filter-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .filter-item-box {
          margin: 0 0 10px 0;
          width: 49%;
          .filter-item-box-item {
            width: 100%;
            border: 1px solid #eee;
          }
        }
      }

      .quickFilter-box {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        align-items: center;
        width: 100%;
        button {
          margin: 1px 5px 0 0;
          font-size: 18px;
          width: calc((100% - 20px) / 4);
        }
        button:nth-of-type(4n + 0) {
          margin-right: 0;
        }
      }
    }
    // .slide-fade-enter-active {
    //   animation: slideDown 0.3s ease-out;
    // }
    // .slide-fade-leave-active {
    //   animation: slideUp 0.3s ease-out;
    // }
    @keyframes dropdown {
      from {
        opacity: 0;
        transform: scaleY(0);
      }
      to {
        opacity: 1;
        transform: scaleY(1);
      }
    }
    @keyframes slideUp {
      from {
        opacity: 1;
        transform: translateY(0) scaleY(1);
      }
      to {
        opacity: 0;
        transform: translateY(-20px) scaleY(0);
      }
    }
    .filter-button-box {
      position: sticky;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 10px 0;
      background: #fff;
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      border-top: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
      button {
        width: 49%;
      }
    }
    .card-box {
      padding: 0 14px;
      margin: 0 0 28px 0;
      // box-shadow: none;
      background: #ffffff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      border-radius: 24px;
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 32px 0 32px;
        padding: 34px 0 0 0;
        .table-switch-tag-download {
          display: flex;
          align-items: center;
          .table-switch-tag {
            margin: 0 2.5px;
            cursor: pointer;
          }

          img {
            margin: 0 0 0 5px;
          }
        }
      }
      p {
        display: flex;
        // justify-content: space-between;
        justify-content: space-around;
        align-items: center;
        flex-wrap: wrap;
        margin: 18px 0 30px 0;
        .table-switch-tag-change {
          // width: 50%;
          // border-radius: 0px;
          width: 275px;
          height: 74px;
          border-radius: 13px;
          border: 2px solid #6a5acd;
          color: #6a5acd;
        }
        :deep(.el-tag--dark) {
          background: #6a5acd;
          color: #fff;
        }
        :deep(.el-tag--plain) {
          width: 275px;
          height: 74px;
          border-radius: 13px;
          background-color: #fff;
          border: 2px solid #6a5acd;
          color: #6a5acd;
        }
      }
      .navigation-box {
        background: #fff;
        padding: 10px 10px;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        margin: 0 0 15px 0;
        p {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          span {
            display: block;
            margin: 0 10px 0 0;
            color: #1576ff;
          }
        }
      }
      .left-fixed-ellipsis {
        display: -webkit-box;
        -webkit-line-clamp: 3; // 限制显示2行
        line-clamp: 3; // 限制显示2行
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: normal;
        line-height: 1.5;
        max-height: 4.5em; // 2行 * 1.5行高
      }
      .default-fixed-ellipsis {
        display: -webkit-box;
        -webkit-line-clamp: 3; // 限制显示2行
        line-clamp: 3; // 限制显示2行
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: normal;
        line-height: 1.5;
        max-height: 4.5em; // 2行 * 1.5行高
      }
      :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
        background: #eee;
      }
      :deep(.el-table--striped .el-table__body tr.el-table__row--striped.current-row td) {
        background: #f5f7fa;
      }
    }

    // .xAPPListTplctr-box {
    //   display: flex;
    //   justify-content: space-between;
    //   align-items: center;
    //   flex-wrap: wrap;
    //   .xAPPListTplctr-box-item {
    //     width: 24.5%;
    //     margin: 0 0 10px 0;
    //     border: 1px solid #eee;
    //     span {
    //       display: block;
    //       overflow: hidden;
    //       text-overflow: ellipsis;
    //       white-space: nowrap;
    //       text-align: center;
    //       margin: 10px 0;
    //     }
    //   }
    //   .xAPPListTplctr-box-item-selected {
    //     background: #1576ff;
    //     color: #fff;
    //   }
    // }
  }

  .operate-box-indicatorNavigationAll {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    padding: 15px 0;
    span {
      width: 100%;
      text-align: center;
    }
    .close-icon {
      position: absolute;
      right: 10px;
      top: 20px;
      color: #969799;
    }
  }
  .indicatorNavigationAll-box {
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-wrap: wrap;
    padding: 10px;
    height: 70vh;
    overflow-y: scroll;
    .indicatorNavigationAll-box-item {
      width: 49%;
      height: 180px;
      margin: 0 0 10px 0;
      border: 1px solid #eee;
      text-align: center;
      border-radius: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      div {
        margin: 5px 0;
        padding: 1px;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .indicatorNavigationAll-box-item-selected {
      background: #1576ff;
      color: #fff;
    }
  }

  .van-popup-box {
    width: 100%;
    height: 70vh;
    padding: 10px;
    overflow: hidden;
  }

  .operate-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    span:first-child {
      color: #969799;
    }
    span:nth-child(2) {
      color: black;
      font-weight: bold;
    }
    span:last-child {
      color: #1989fa;
    }
  }
  .van-popup-tree-box {
    margin: 15px 30px;
    padding: 0 0 100px 0;
    background: #fff;
    height: 100%;
    overflow: hidden;
    overflow-y: scroll;
  }
}
</style>
