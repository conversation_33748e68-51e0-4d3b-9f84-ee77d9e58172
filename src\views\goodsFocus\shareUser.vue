<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import 'element-plus/dist/index.css'
import { useRoute, useRouter } from 'vue-router'
import { showConfirmDialog, showSuccessToast, showFailToast } from 'vant'
import { selectUserTree, selectPermissionGroupList, selectUserInfoByGroupIds } from '@/api/shareUser'
import { shareGroup } from '@/api/goodsFocus'



const route = useRoute()
const router = useRouter()
const shareGroupId = ref('')
const shareUserTabActive = ref(0)
const lastTagActive = ref(0)
const shareUserSelected = ref([])
const activeOrgName = ref([])
const userCheckboxRefs = ref([])
const groupSelected = ref([])
const groupSelectedUser = ref({})
const orgTree = ref([])
const storeGroup = ref([])
const storeCheckboxRefs = ref([])
const categoryGroup = ref([])
const categoryCheckboxRefs = ref([])
const reportGroup = ref([])
const reportCheckboxRefs = ref([])
const showUserSelected = ref(false)
const showUserSearch = ref(false)
const searchGroupName = ref('')
const searchGroupUser = ref([])


// 提交分享
const submitShare = () => {
  shareGroup({
    groupId: shareGroupId.value,
    assignList: shareUserSelected.value.map(item => item.id),
  }).then((res) => {
    console.log(res, 'submitShare===')
    showSuccessToast('分享成功')
    router.go(-1)
  }).catch((error) => {
    console.log('==分享失败==', error)
    showFailToast('分享失败')
  })
}
// 加载用户树
const loadUserTree = (searchName) => {
  selectUserTree({
    dutyId: "",
    name: searchName,
    type: "",
    configCode: "",
  }).then((res) => {
    console.log(res)
    orgTree.value = res
  }).catch((error) => {
    console.log('==获取用户树失败==', error)
  })
}
// 加载组别
const loadPermissionGroup = (keyword, type) => {
  selectPermissionGroupList({
    keyword: keyword,
    type: type,
  }).then((res) => {
    console.log(res)
    if (type === 1) {
      storeGroup.value = res
    } else if (type === 2) {
      categoryGroup.value = res
    } else if (type === 3) {
      reportGroup.value = res
    }
  }).catch((error) => {
    console.log('==获取权限组失败==', error)
  })
}
// 点击tab
const onClickTab = (tab) => {
  console.log(shareUserTabActive)
  console.log(tab, 'tab')
  if (shareUserSelected.value.length > 0) {
    showConfirmDialog({
      title: '提示',
      message:
        '重新选择分组后，将会覆盖当前已选用户',
    })
    .then(() => {
      lastTagActive.value = tab.name
      shareUserSelected.value = []
      groupSelectedUser.value = {}
      groupSelected.value = []
      nextTick().then(() => {
        storeCheckboxRefs.value?.toggleAll(false)
        categoryCheckboxRefs.value?.toggleAll(false)
        reportCheckboxRefs.value?.toggleAll(false)
        userCheckboxRefs.value?.toggleAll(false)
      })
    })
    .catch(() => {
      shareUserTabActive.value = lastTagActive.value
    });
  } else {
    lastTagActive.value = tab.name
  }
}
// 根据组id搜索用户
const searchUser = (groupId, type, groupName) => {
  console.log(groupId, 'groupId')
  selectUserInfoByGroupIds({
    storeGroupIds: type === 'store' ? [groupId] : [],
    categoryGroupIds: type === 'category' ? [groupId] : [],
    reportGroupIds: type === 'report' ? [groupId] : [],
  }).then((res) => {
    console.log(res, 'res===')
    searchGroupUser.value = res
    searchGroupName.value = groupName
    showUserSearch.value = true
  }).catch((error) => {
    console.log('==获取用户信息失败==', error)
  })
}
// 选择组
const groupToggle = (index, type) => {
  let gId = ''
  if (type === 'store') {
    storeCheckboxRefs.value[index].toggle()
    gId = storeGroup.value[index].id
  } else if (type === 'category') {
    categoryCheckboxRefs.value[index].toggle()
    gId = categoryGroup.value[index].id
  } else if (type === 'report') {
    reportCheckboxRefs.value[index].toggle()
    gId = reportGroup.value[index].id
  } else if (type === 'user') {
    userCheckboxRefs.value[index].toggle()
    return
  }
  console.log(gId, 'gId')
  if (groupSelectedUser.value[gId]) {
    shareUserSelected.value = shareUserSelected.value.filter(item => !groupSelectedUser.value[gId].find(user => user.id === item.id))
    delete groupSelectedUser.value[gId]
  } else {
    selectUserInfoByGroupIds({
      storeGroupIds: type === 'store' ? [gId] : [],
      categoryGroupIds: type === 'category' ? [gId] : [],
      reportGroupIds: type === 'report' ? [gId] : [],
    }).then((res) => {
      console.log(res, 'res===')
      groupSelectedUser.value[gId] = res
      console.log(groupSelectedUser.value, 'groupSelectedUser.value')
      shareUserSelected.value.push(...res)
      console.log(shareUserSelected.value, 'shareUserSelected.value')
    }).catch((error) => {
      console.log('==获取用户信息失败==', error)
    })
  }
}

onMounted(() => {
  document.title = '抄送人'
  if (route.query.g) {
    shareGroupId.value = route.query.g
    console.log(shareGroupId.value, 'shareGroupId.value')
  }
  // 用户树
  loadUserTree('')
  // 门店组
  loadPermissionGroup('', 1)
  // 类别组
  loadPermissionGroup('', 2)
  // 职位组
  loadPermissionGroup('', 3)
})
onBeforeUnmount(() => {
  shareUserSelected.value = []
})

</script>

<template>
  <div class="share-user">
    <div class="share-user-container">
      <van-tabs v-model:active="shareUserTabActive" class="share-tabs" @click-tab="onClickTab">
        <van-tab title="手动添加">
          <van-sticky>
            <div class="search-container">
              <van-search
                v-model="searchName"
                show-action
                placeholder="请输入名称搜索"
                @search="loadUserTree(searchName)"
              >
                <template #action>
                  <van-button @click="loadUserTree(searchName)" type="primary" size="small">搜索</van-button>
                </template>
              </van-search>
            </div>
          </van-sticky>
          <div class="divider"></div>
          <div class="user-list-container">
            <van-collapse v-model="activeOrgName" accordion>
              <van-collapse-item v-bind:key="item.id" v-for="item in orgTree" :title="item.name" :name="item.id">
                <van-checkbox-group v-model="shareUserSelected">
                  <van-cell-group inset>
                    <van-cell
                      v-for="(user, index) in item.userTree"
                      clickable
                      :key="user.id"
                      :title="`${user.userName}(${user.roleName})`"
                      @click="groupToggle(index, 'user')"
                    >
                      <template #right-icon>
                        <van-checkbox
                          :name="user"
                          :ref="el => userCheckboxRefs[index] = el"
                          @click.stop
                        />
                      </template>
                    </van-cell>
                  </van-cell-group>
                </van-checkbox-group>
              </van-collapse-item>
            </van-collapse>
          </div>
        </van-tab>
        <van-tab title="门店组">
          <div class="group-list-container">
            <van-checkbox-group v-model="groupSelected">
              <van-cell-group inset>
                <van-cell
                  v-for="(group, index) in storeGroup"
                  :key="group.id"
                  :title="group.name"
                >
                  <template #right-icon>
                    <van-space>
                      <van-icon name="search" size="1.5rem" @click="searchUser(group.id, 'store', group.name)" />
                      <van-checkbox
                        :name="group.id"
                        :ref="el => storeCheckboxRefs[index] = el"
                        @click="groupToggle(index, 'store')"
                      />
                    </van-space>
                  </template>
                </van-cell>
              </van-cell-group>
            </van-checkbox-group>
          </div>
        </van-tab>
        <van-tab title="类别组">
          <div class="group-list-container">
            <van-checkbox-group v-model="groupSelected">
              <van-cell-group inset>
                <van-cell
                  v-for="(group, index) in categoryGroup"
                  :key="group.id"
                  :title="group.name"
                >
                  <template #right-icon>
                    <van-space>
                      <van-icon name="search" size="1.5rem" @click="searchUser(group.id, 'category', group.name)" />
                      <van-checkbox
                        :name="group.id"
                        :ref="el => categoryCheckboxRefs[index] = el"
                        @click="groupToggle(index, 'category')"
                      />
                    </van-space>
                  </template>
                </van-cell>
              </van-cell-group>
            </van-checkbox-group>
          </div>
        </van-tab>
        <van-tab title="职位组">
          <div class="group-list-container">
            <van-checkbox-group v-model="groupSelected">
              <van-cell-group inset>
                <van-cell
                  v-for="(group, index) in reportGroup"
                  :key="group.id"
                  :title="group.name"
                >
                  <template #right-icon>
                    <van-space>
                      <van-icon name="search" size="1.5rem" @click="searchUser(group.id, 'report', group.name)" />
                      <van-checkbox
                        :name="group.id"
                        :ref="el => reportCheckboxRefs[index] = el"
                        @click="groupToggle(index, 'report')"
                      />
                    </van-space>
                  </template>
                </van-cell>
              </van-cell-group>
            </van-checkbox-group>
          </div>
        </van-tab>
      </van-tabs>
    </div>
    <div class="footer">
      <div class="selected-count" @click="showUserSelected = true">
        已选择：{{ shareUserSelected.length }}人
      </div>
      <van-button @click="submitShare" type="primary" size="small">确定</van-button>
    </div>
    <van-popup
      v-model:show="showUserSelected"
      closeable
      position="bottom"
      :style="{ height: '70%' }"
    >
      <div class="popup-container">
        <van-nav-bar title="已选择用户清单" class="popup-header" />
        <div class="popup-content">
          <van-list>
            <van-grid :column-num="3">
              <van-grid-item text="序号" />
              <van-grid-item text="姓名" />
              <van-grid-item text="手机号" />
            </van-grid>
            <van-grid :column-num="3" v-for="(item, index) in shareUserSelected" :key="item.id">
              <van-grid-item :text="index+1" />
              <van-grid-item :text="item.realName ? item.realName : item.userName" />
              <van-grid-item :text="item.mobilePhone ? item.mobilePhone.slice(0, 3) + '****' + item.mobilePhone.slice(-4) : ''" />
            </van-grid>
          </van-list>
        </div>
      </div>
    </van-popup>
    <van-popup
      v-model:show="showUserSearch"
      closeable
      position="bottom"
      :style="{ height: '100%' }"
    >
      <div class="popup-container">
        <van-nav-bar :title="`${searchGroupName} 用户清单`" class="popup-header" />
        <div class="popup-content">
          <van-list>
            <van-grid :column-num="3">
              <van-grid-item text="序号" />
              <van-grid-item text="姓名" />
              <van-grid-item text="手机号" />
            </van-grid>
            <van-grid :column-num="3" v-for="(item, index) in searchGroupUser" :key="item.id">
              <van-grid-item :text="index+1" />
              <van-grid-item :text="item.realName ? item.realName : item.userName" />
              <van-grid-item :text="item.mobilePhone ? item.mobilePhone.slice(0, 3) + '****' + item.mobilePhone.slice(-4) : ''" />
            </van-grid>
          </van-list>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<style scoped lang="less">
.share-user {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .share-user-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .share-tabs {
    display: flex;
    flex-direction: column;
    height: 100%;

    :deep(.van-tabs__content) {
      flex: 1;
      overflow: auto;
    }
  }

  .search-container {
    padding: 8px 0;
  }

  .divider {
    height: 1px;
    background-color: #ebedf0;
    margin: 0 16px;
  }

  .user-list-container,
  .group-list-container {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 16px;
    background-color: #fff;
    border-top: 1px solid #ebedf0;

    .selected-count {
      flex: 1;
    }
  }

  .popup-container {
    display: flex;
    flex-direction: column;
    height: 100%;

    .popup-header {
      flex-shrink: 0;
    }

    .popup-content {
      flex: 1;
      overflow-y: auto;
    }
  }
}
</style>
