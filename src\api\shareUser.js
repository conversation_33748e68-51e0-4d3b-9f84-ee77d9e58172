import axios from '@/network/axios' // 导入配置好的 axios 实例


// 获取用户部门树
export const selectUserTree = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/user/selectUserTree',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// 查询权限组列表
export const selectPermissionGroupList = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/permissionGroup/selectPermissionGroupList',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// 根据组id查询用户信息
export const selectUserInfoByGroupIds = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/user/selectUserInfoByGroupIds',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}
