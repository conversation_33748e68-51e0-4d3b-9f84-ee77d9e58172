import axios from '@/network/axios' // 导入配置好的 axios 实例


// 获取企业体检数据
export const getShopCheckData = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/goodsFocus/selectUserGoodsGroupList',
      },
    })
    // return response.result
    return {
      shopid: '10',

      salevalue_finishrate: 80,
      salevalue_finishrate_rank: 32,
      prev_salevalue_finishrate_rank: 35,
      median_salevalue_finishrate: 68,
      salevalue_finishrate_str: '80%',
      median_salevalue_finishrate_str: '68%',
      max_salevalue_finishrate: 100,

      avg_customer_price: 85,
      avg_customer_price_rank: 12,
      prev_avg_customer_price_rank: 10,
      avg_customer_price_str: '85',
      median_avg_customer_price_str: '75',
      max_avg_customer_price: 100,
      median_avg_customer_price: 75,

      avg_num_purchases: 90,
      avg_num_purchases_rank: 7,
      prev_avg_num_purchases_rank: 7,
      avg_num_purchases_str: '90',
      median_avg_num_purchases_str: '80',
      max_avg_num_purchases: 100,
      median_avg_num_purchases: 80,

      abysku_rate: 40,
      abysku_rate_rank: 56,
      prev_abysku_rate_rank: 58,
      abysku_rate_str: '40%',
      median_abysku_rate_str: '50%',
      max_abysku_rate: 100,
      median_abysku_rate: 50,

      profit_rate: 60,
      profit_rate_rank: 23,
      prev_profit_rate_rank: 25,
      profit_rate_str: '60%',
      median_profit_rate_str: '55%',
      max_profit_rate: 100,
      median_profit_rate: 55,

      sheetqty: 124,
      sheetqty_rank: 12,
      prev_sheetqty_rank: 12,
      sheetqty_str: '124',
      median_sheetqty_str: '223',
      max_sheetqty: 1000,
      median_sheetqty: 223,
    }
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}
