<script setup>
    import { computed, ref, watch, h, onMounted, nextTick, getCurrentInstance } from 'vue'
    import { Badge, Button, message, Space, theme, Typography } from 'ant-design-vue'
    import {
        Bubble,
        Conversations,
        Prompts,
        Sender,
        Welcome,
        useXAgent,
        useXChat,
    } from 'ant-design-x-vue'
    import {
        CommentOutlined,
        CopyOutlined,
        EllipsisOutlined,
        FireOutlined,
        HeartOutlined,
        PaperClipOutlined,
        PlusOutlined,
        ReadOutlined,
        ShareAltOutlined,
        SmileOutlined,
        SyncOutlined,
        UserOutlined,
    } from '@ant-design/icons-vue'
    import markdownIt from 'markdown-it'
    import hljs from 'highlight.js'
    import * as echarts from 'echarts'
    import { api } from '@/api/ai'
    import { useRoute } from 'vue-router'

    const md = markdownIt({
        html: true,
        breaks: true,
        linkify: true,
        highlight: function (str, lang) {
            if (lang && hljs.getLanguage(lang)) {
                try {
                    return hljs.highlight(str, { language: lang }).value
                } catch (__) { }
            }
            return ''
        },
    })

    // 自定义渲染器：处理 echarts 代码块
    md.renderer.rules.fence = (tokens, idx) => {
        const token = tokens[idx]
        const code = token.content.trim()
        const lang = token.info.trim()

        if (lang === 'echarts') {
            try {
                const jsonData = JSON.parse(code)
                const chartId = `chart-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

                nextTick(() => {
                    const chartElement = document.getElementById(chartId)
                    if (chartElement) {
                        const chart = echarts.init(chartElement)
                        chart.setOption(jsonData)
                    }
                })

                return `<div id="${chartId}" style="width: 100%; height: 300px;"></div>`
            } catch (error) {
                // console.error('JSON 解析失败:', error);
                return `<pre><code>${code}</code></pre>`
            }
        }

        return `<pre><code class="language-${lang}">${code}</code></pre>`
    }

  // 自定义渲染器：处理 表格样式
  md.renderer.rules.table_open = (tokens, idx, options, env, self) => {
    return '<div class="table-responsive box-shadow-wrap-lg"><table>'
  }
  md.renderer.rules.table_close = (tokens, idx, options, env, self) => {
    return '</table></div>'
  }


    // ==================== Style ====================
    const { token } = theme.useToken()
    const styles = computed(() => {
        return {
            layout: {
                width: '100%',
                'min-width': '1000px',
                height: '722px',
                'border-radius': `${token.value.borderRadius}px`,
                display: 'flex',
                background: `${token.value.colorBgContainer}`,
                'font-family': `AlibabaPuHuiTi, ${token.value.fontFamily}, sans-serif`,
            },
            menu: {
                background: `${token.value.colorBgLayout}80`,
                width: '280px',
                height: '100%',
                display: 'flex',
                'flex-direction': 'column',
            },
            conversations: {
                padding: '0 12px',
                flex: 1,
                'overflow-y': 'auto',
            },
            chat: {
                height: '100%',
                width: '100%',
                'max-width': '700px',
                margin: '0 auto',
                'box-sizing': 'border-box',
                display: 'flex',
                'flex-direction': 'column',
                padding: `${token.value.paddingLG}px`,
                gap: '16px',
            },
            messages: {
                flex: 1,
            },
            placeholder: {
                'padding-top': '32px',
            },
            sender: {
                'box-shadow': token.value.boxShadow,
            },
            logo: {
                display: 'flex',
                height: '72px',
                'align-items': 'center',
                'justify-content': 'start',
                padding: '0 24px',
                'box-sizing': 'border-box',
            },
            'logo-img': {
                width: '24px',
                height: '24px',
                display: 'inline-block',
            },
            'logo-span': {
                display: 'inline-block',
                margin: '0 8px',
                'font-weight': 'bold',
                color: token.value.colorText,
                'font-size': '16px',
            },
            addBtn: {
                background: '#1677ff0f',
                border: '1px solid #1677ff34',
                width: 'calc(100% - 24px)',
                margin: '0 12px 24px 12px',
            },
        }
    })

    const roles = {
        ai: {
            placement: 'start',
            avatar: { icon: h(UserOutlined), style: { background: '#fde3cf' } },
            typing: { step: 5, interval: 50 },
            styles: {
                content: {
                    borderRadius: '16px',
                },
            },
            messageRender: (content) =>
                h(Typography, null, {
                    default: () => h('div', { innerHTML: md.render(content) }),
                }),
            footer: h(Space, { size: token.value.paddingXXS }, [
                h(Button, { type: 'text', size: 'small', icon: h(SyncOutlined) }),
                h(Button, { type: 'text', size: 'small', icon: h(CopyOutlined) }),
            ]),
        },
        user: {
            placement: 'end',
            variant: 'shadow',
            avatar: { icon: h(UserOutlined), style: { background: '#87d068' } },
        },
        suggestion: {
            placement: 'start',
            avatar: { icon: UserOutlined, style: { visibility: 'hidden' } },
            variant: 'borderless',
            messageRender: (content) => h(Prompts, {
                vertical: true,
                items: (content).map((text) => ({
                    key: text,
                    icon: h(SmileOutlined, { style: { color: '#FAAD14' } }),
                    description: text,
                }))
            }),
        },

    }
    

    // ==================== State ====================
    const headerOpen = ref(false)
    const submitContent = ref('')
    const conversationsItems = ref([])
    const activeKey = ref('')
    const cozeConversationId = ref('')
    const cozeBotId = ref('')
    const attachedFiles = ref([])
    const agentRequestLoading = ref(false)
    const answerFinished = ref(false)
    const conversationList = ref([])
    const suggestionList = ref([])

    // ==================== Runtime ====================

    const abortControllerRef = ref(null)
    const aiMsgContent = ref('')

    const [agent] = useXAgent({
        request: async (info, callbacks) => {
            const { messages, message } = info
            const { onUpdate, onSuccess } = callbacks

            // console.log('before submit msg, checkup messages: ', messages);

            aiMsgContent.value = ''

            agentRequestLoading.value = true;
            // roles.ai.typing = { step: 5, interval: 50 };

            // 更新会话标题
            const title = message.substring(0, 20);
            conversationsItems.value.map((item) => {
                if (item.key == activeKey.value) {
                    item.label = title;
                }
            })

            if (abortControllerRef.value) {
                abortControllerRef.value.abort()
            }

            const abortController = new AbortController()
            abortControllerRef.value = abortController

            try {
                const response = await fetch(
                    'http://localhost:3000/saas-operations-api/saasapi/nexora/chat/stream',
                    {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Cache-Control': 'no-cache',
                            'sq-token': 'f47bf52f7e226f3739a2ecfc92519670',
                            'vdf-source': 'vdf-front',
                            'x-user-info': 'f47bf52f7e226f3739a2ecfc92519670',
                        },
                        body: JSON.stringify({
                            prompt: message,
                            conversationId: activeKey.value,
                            cozeConversationId: cozeConversationId.value,
                            cozeBotId: cozeBotId.value,
                        }),
                        signal: abortController.signal,
                    },
                )

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`)
                }

                const reader = response.body?.getReader()
                if (!reader) throw new Error('No reader available')

                const decoder = new TextDecoder()

                // agentRequestLoading.value = false;

                while (true) {
                    const { done, value } = await reader.read()
                    if (done) break

                    const chunkStr = decoder.decode(value)

                    const lines = chunkStr.split('\n').filter(Boolean)

                    for (const line of lines) {
                        const sseString = line.startsWith('data:') ? line.slice('data:'.length) : line
                        if (sseString.length < 1) continue
                        let sseEvent
                        try {
                            sseEvent = JSON.parse(sseString)
                        } catch (err) {
                            console.error('Error parsing SSE line:', err, line)
                            continue
                        }

                        // 消息
                        aiMsgContent.value += sseEvent.message
                        // suggestion 列表
                        suggestionList.value = sseEvent.suggestions

                        console.log('onUpdate')
                        onUpdate(aiMsgContent.value)
                        
                        // 更新当前对话的coze会话id，botId
                        cozeConversationId.value = sseEvent.conversationId
                        cozeBotId.value = sseEvent.botId
                    }
                }
            } catch (error) {
                console.error('Error sending message:', error)
                agentRequestLoading.value = false

                aiMsgContent.value = '系统繁忙，请稍后再试~'
                onUpdate(aiMsgContent.value)

            } finally {
                agentRequestLoading.value = false
                abortControllerRef.value = null
                answerFinished.value = true

                
            }
            onSuccess(aiMsgContent.value);
        },
    })

    const { onRequest, messages, setMessages } = useXChat({
        agent: agent.value,
        requestPlaceholder: 'Waiting...',
    })

    // ==================== events ===================
    const onAddConversation = () => {
        api.newConversation()
            .then((res) => {

                const newConversation = conversationsItems.value.filter((item) => item.key == res.result.id);
                if (newConversation.length > 0) {
                    activeKey.value = res.result.id;
                    setMessages([]);
                    // TODO 弹出消息提示已是最新会话

                } else {
                    conversationsItems.value = [
                        {
                            key: res.result.id,
                            label: '新会话',
                        },
                        ...conversationsItems.value
                    ]
                    activeKey.value = res.result.id;
                    cozeConversationId.value = '';
                    setMessages([]);
                }

            })
            .catch((error) => {
                console.log(error)
            })
    }

    const onConversationClick = (key) => {
        activeKey.value = key
        if (cozeConversationId.value != key) {
            cozeConversationId.value = ''
        }

        initMessages()
    }

    const onPromptsItemClick = (info) => {
        console.log('Prompt clicked:', info.data.key)
    }

    const handleFileChange = (info) => (attachedFiles.value = info.fileList)

    const handleFile = () => {
        headerOpen.value = !headerOpen.value
        console.log('headerOpen.value', headerOpen.value)
    }

    const onSubmit = (nextContent) => {
        if (!nextContent) return
        // 向bubblelist推入新消息，发起新请求
        onRequest(nextContent)
        submitContent.value = ''

        // 设置ai消息不用打字机效果
        // roles.ai.typing = false
        
    }

    // ==================== Nodes ====================

    let bubbleItems = computed(() => {
        return messages.value.map(({ id, message, status }) => ({
            key: id,
            role: status === 'local' ? 'user' : 'ai',
            loading: message.length === 0,
            content: message,
        }))
    }
        
    )

    const renderTitle = (icon, title) => {
        return h(Space, { align: 'start' }, [icon, h('span', {}, title)])
    }

    const placeholderPromptsItems = [
        {
            key: '1',
            label: renderTitle(h(FireOutlined, { color: '#FF4D4F' }), 'Hot Topics'),
            description: 'What are you interested in?',
            children: [
                {
                    key: '1-1',
                    description: `What's new in X?`,
                },
                {
                    key: '1-2',
                    description: `What's AGI?`,
                },
                {
                    key: '1-3',
                    description: `Where is the doc?`,
                },
            ],
        },
        {
            key: '2',
            label: renderTitle(h(ReadOutlined, { color: '#1890FF' }), 'Design Guide'),
            description: 'How to design a good product?',
            children: [
                {
                    key: '2-1',
                    icon: h(HeartOutlined),
                    description: `Know the well`,
                },
                {
                    key: '2-2',
                    icon: h(SmileOutlined),
                    description: `Set the AI role`,
                },
                {
                    key: '2-3',
                    icon: h(CommentOutlined),
                    description: `Express the feeling`,
                },
            ],
        },
    ]

    const senderPromptsItems = [
        {
            key: '1',
            description: 'Hot Topics',
            icon: h(FireOutlined, { color: '#FF4D4F' }),
        },
        {
            key: '2',
            description: 'Design Guide',
            icon: h(ReadOutlined, { color: '#1890FF' }),
        },
    ]

    const initConversations = async () => {
        await api
            .conversationList({})
            .then((res) => {
                res.result.map((e) => {
                    conversationsItems.value.push({
                        key: e.id,
                        label: e.title ? e.title : '新会话',
                    })
                })
                conversationList.value = res.result
                activeKey.value = res.result[0].id
                const msgs = res.result[0].messagesInfo
                if (msgs.length > 0) {
                    cozeConversationId.value = msgs[msgs.length - 1].conversationId || '';
                    cozeBotId.value = msgs[msgs.length - 1].botId || '';
                }
            })
            .catch((error) => {
                console.log(error)
            })
    }

    const initUserInfo = async (token) => {
        localStorage.setItem('setToken', token)
    }

    const initMessages = () => {
        if (conversationList.value.length > 0) {

            // 获取对话中的消息列表
            api.conversationDetail(activeKey.value).then((res) => {

                const messageInfo = res.result.messagesInfo;

                // 取会话中，最后调用的conversationId, botId
                if (messageInfo.length > 0) {
                    let msgs = [];
                    // 取会话消息列表
                    messageInfo.forEach((e, index) => {
                        msgs.push({
                            id: res.result.id + '_' + index,
                            status: e.role == 'user' ? 'local' : 'success',
                            message: e.content,
                        })
                    });
                    roles.ai.typing = false;
                    setMessages(msgs);

                    const aiMsgs = messageInfo.filter(msg => msg.role == 'assistant');
                    if (aiMsgs.length > 0) {
                        cozeConversationId.value = aiMsgs[aiMsgs.length - 1].conversationId
                        cozeBotId.value = aiMsgs[aiMsgs.length - 1].botId
                    }
                }

            }).catch(error => {
                console.log(error);
            })
        }
        setMessages([])
    }

    const route = useRoute()
    onMounted(async () => {
        // 初始化用户信息
        await initUserInfo(route.query.token + '')
        // 初始化会话
        await initConversations()
        // 初始化会话内的消息
        await initMessages()
    })
</script>

<template>
    <div :style="styles.layout">
        <div :style="styles.menu">
            <div :style="styles.logo">
                <img src="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*eco6RrQhxbMAAAAAAAAAAAAADgCCAQ/original"
                    draggable="false" alt="logo" :style="styles['logo-img']" />
                <span :style="styles['logo-span']">Ant Design X Vue</span>
            </div>
            <Button @click="onAddConversation" type="link" :style="styles.addBtn">
                <PlusOutlined />
                新会话
            </Button>
            <!-- 🌟 会话管理 -->
            <Conversations :items="conversationsItems" :style="styles.conversations" :activeKey="activeKey"
                @activeChange="onConversationClick" />
        </div>
        <div :style="styles.chat">
            <Space v-if="!bubbleItems || bubbleItems.length == 0" direction="vertical" :size="16"
                :style="styles.placeholder">
                <Welcome variant="borderless"
                    icon="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp"
                    title="Hello, I'm Ant Design X"
                    description="Base on Ant Design, AGI product interface solution, create a better intelligent vision~">
                    <template #actions>
                        {() => (
                        <Space>
                            <Button :icon="h(ShareAltOutlined)" />
                            <Button :icon="h(EllipsisOutlined)" />
                        </Space>
                        )}
                    </template>
                </Welcome>
                <Prompts title="Do you want?" :items="placeholderPromptsItems" :styles="{
                    list: {
                        width: '100%',
                    },
                    item: {
                        flex: 1,
                    },
                }" @itemClick="onPromptsItemClick" />
            </Space>

            <!-- 🌟 消息列表 -->
            <Bubble.List v-if="bubbleItems && bubbleItems.length > 0" :items="bubbleItems" :roles="roles"
                :loading="agentRequestLoading" :style="styles.messages" />

            <!-- 🌟 提示词 
      <Prompts :style="{ color: token.colorText }" :items="senderPromptsItems" @itemClick="onPromptsItemClick" />
      -->

            <!-- 🌟 输入框 -->
            <Sender :value="submitContent" @submit="onSubmit" :style="styles.sender">
                <template #prefix>
                    <Badge :dot="attachedFiles.length > 0 && !headerOpen">
                        <Button type="text" :icon="h(PaperClipOutlined)" @click="handleFile" />
                    </Badge>
                </template>
            </Sender>
        </div>
    </div>
</template>

<style lang="less">

    /* 表格容器样式 */
    .table-responsive {
        max-width: 100%;
        /* 不超过父容器宽度 */
        overflow-x: auto;
        /* 启用横向滚动 */
        -webkit-overflow-scrolling: touch;
        /* 移动端滚动优化 */
        margin: 1rem 0;
        border-radius: 8px;
    }

    /* 表格主体样式 */
    .table-responsive table {
        width: 100%;
        min-width: 600px;
        /* 保证内容较多时正常换行 */
        border-collapse: collapse;
        background: white;
    }

    /* 斑马纹效果 */
    .table-responsive tr:nth-child(even) {
        background-color: #f8f9fa;
        /* 浅灰色背景 */
    }

    /* 单元格基础样式 */
    .table-responsive td {
        padding: 12px 16px;
        border-bottom: 1px solid #dee2e6;
        white-space: nowrap;
        /* 禁止自动换行 */
    }

    /* 表头样式 */
    .table-responsive th {
        padding: 16px;
        background: #2c3e50;
        /* 深蓝色背景 */
        color: white;
        font-weight: 600;
        text-align: left;
        position: sticky;
        left: 0;
        /* 固定表头（横向滚动时） */
    }

    /* 悬停效果 */
    .table-responsive tr:hover td {
        background-color: #e9ecef;
        /* 更深的悬停色 */
    }

    /* 阴影容器（你已有的 box-shadow-wrap-lg） */
    .box-shadow-wrap-lg {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
            0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
</style>