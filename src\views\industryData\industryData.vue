<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { useRoute } from 'vue-router'
const route = useRoute()
import { searchBarcode } from '@/api/industryData'
import { industryDataLineChart } from '@/echarts/echarts_Js/echartsJs'
import echartsDemo from '@/components/echarts/echartsDemo.vue'

// 创建响应式的 echartsData
const chartExample = ref()
// 图表图例点击方法
const echartsLegendMethod = (item) => {
  // item.selected = !item.selected
  for (let i = 0; i < priceTrendBottonList.length; i++) {
    if (item.label === priceTrendBottonList[i].label) {
      priceTrendBottonList[i].selected = !priceTrendBottonList[i].selected
    }
  }
  chartExample.value.handleLegendSelectChanged(item)
  console.log(chartExample.value, 'chartExample.value')
  // 更新图表显示
}
const handleLegendUpdate = ({ label, selected }) => {
  chartConfig.chart.legend.selected[label] = selected
}

// 区间价格按钮操作方法
const operateMethod = (item, index) => {
  // item.state = !item.state
  if (index === buttonIndex.value) {
    if (
      incomePriceInterval[index].buttonType === 'info' ||
      salePriceInterval[index].buttonType === 'info'
    ) {
      incomePriceInterval[index].buttonType = 'default'
      salePriceInterval[index].buttonType = 'default'
      incomePriceInterval[index].state = false
      salePriceInterval[index].state = false
      buttonIndex.value = index
    } else {
      buttonIndex.value = null
      incomePriceInterval[index].buttonType = 'info'
      salePriceInterval[index].buttonType = 'info'
      incomePriceInterval[index].state = true
      salePriceInterval[index].state = true
    }
  } else {
    buttonIndex.value = index
    if (
      incomePriceInterval[index].buttonType === 'default' ||
      salePriceInterval[index].buttonType === 'default'
    ) {
      incomePriceInterval[index].buttonType = 'info'
      salePriceInterval[index].buttonType = 'info'
      incomePriceInterval[index].state = true
      salePriceInterval[index].state = true
    } else {
      incomePriceInterval[index].buttonType = 'default'
      incomePriceInterval[index].state = false
      salePriceInterval[index].buttonType = 'default'
      salePriceInterval[index].state = false
    }
  }
}

const sqToken = ref()
const orgId = ref('')
const appCode = ref('')
const barcode = ref('')
const bizTypeId = ref('')
const regionTypeId = ref('')
const smonth = ref('')
const fType = ref('')

const loginUserData = reactive({
  token: '',
  sqToken: '',
  saasToken: '',
}) //用户登录信息

// 行业数据信息
const returnInfo = reactive({ itemInfo: {} })
// 价格趋势按钮列表
const priceTrendBottonList = reactive([])

const buttonIndex = ref(null) //区间价格图表点击下标

const incomePriceIntervalAll = reactive([])

const salePriceIntervalAll = reactive([])

// 区间价格数据
const incomePriceInterval = reactive([])

const salePriceInterval = reactive([])

const incomePriceIntervalMin = ref(null)

const incomePriceIntervalMax = ref(null)

const salePriceIntervalMin = ref(null)

const salePriceIntervalMax = ref(null)

const chartConfig = reactive({})

import axios from 'axios'

const getBarcode = async () => {
  let data = {
    orgId: orgId.value,
    barcode: barcode.value,
  }
  console.log(data, 'data')

  try {
    const res = await searchBarcode(data)
    console.log(res, 'res-getBarcode')
    Object.assign(returnInfo, res)
    initData()
  } catch (error) {
    console.log(error)
  }
}

// const initLogin = async () => {
//   const customAxios = axios.create({
//     baseURL: 'https://saas.viwor.net/',
//   })
//   let data = {
//     accessKey: '',
//     format: '',
//     param: {
//       sqToken: sqToken.value,
//       orgId: orgId.value,
//       appCode: appCode.value,
//     },
//     sign: '',
//     source: '',
//     timestamp: '',
//     version: '',
//   }
//   try {
//     const res = await customAxios.post('saas-dm-api/api/appauth/login', data)
//     console.log(res, 'res-initLogin')
//     Object.assign(loginUserData, res.data.result)
//     initData(res.data.result.token, res.data.result.sqToken, res.data.result.saasToken)
//   } catch (error) {}
// }

const initData = async (bizTypeIdParams, regionTypeIdParams) => {
  // const customAxios = axios.create({
  //   baseURL: 'https://saas.viwor.net/',
  //   headers: {
  //     'x-token': loginUserData.token,
  //     'x-sq-token': loginUserData.sqToken,
  //     'x-saas-token': loginUserData.saasToken,
  //   },
  // })

  // let data = {
  //   accessKey: '',
  //   format: '',
  //   param: {
  //     orgId: orgId.value,
  //     barcode: barcode.value,
  //     bizTypeId: bizTypeId.value * 1,
  //     regionTypeId: regionTypeId.value * 1,
  //     smonth: smonth.value * 1,
  //     forwardType: fType.value ? fType.value * 1 : -1,
  //   },
  //   sign: '',
  //   source: '',
  //   timestamp: '',
  //   version: '',
  // }
  // try {
  // const res = await customAxios.post(
  //   'saas-dm-api/api/industry/data/price/compare/search/barcode',
  //   data,
  // )
  // Object.assign(returnInfo, res.data.result)
  console.log(returnInfo, 'returnInfo')
  let legendData = returnInfo.charts.legendList

  priceTrendBottonList.length = 0
  let initPriceTrendBotton = [
    { label: '', color: '#ABD895', name: '', selected: false },
    { label: '', color: '#8397D6', name: '', selected: false },
    { label: '', color: '#FC9065', name: '', selected: false },
    { label: '', color: '#EF6E6E', name: '', selected: false },
    { label: '', color: '#FAC95D', name: '', selected: false },
    { label: '', color: '#52AE83', name: '', selected: false },
    { label: '', color: '#89CBE4', name: '', selected: false },
    { label: '', color: '#A36FBC', name: '', selected: false },
  ]
  for (let i = 0; i < initPriceTrendBotton.length; i++) {
    initPriceTrendBotton[i].label = legendData[i]
    initPriceTrendBotton[i].name = legendData[i].replace(/(销售价|进价|采购价)/, '')
    if (initPriceTrendBotton[i].name === '中位数' || initPriceTrendBotton[i].name === '平均数') {
      initPriceTrendBotton[i].selected = true
    } else {
      initPriceTrendBotton[i].selected = false
    }
    priceTrendBottonList.push(initPriceTrendBotton[i])
  }

  let legendSelected = {}
  legendData.forEach((value) => {
    // if (value === '采购价中位数' || value === '采购价平均数' || value === '销售价中位数' || value === '销售价平均数') {
    //   legendSelected[value] = true
    // } else {
    //   legendSelected[value] = false
    // }
    //  暂时去除 平均数
    if (value === '采购价中位数' || value === '销售价中位数') {
      legendSelected[value] = true
    } else {
      legendSelected[value] = false
    }
    //  暂时去除 平均数
  })
  let timeData = returnInfo.charts.xaxis
  let seriesData = returnInfo.charts.seriesList

  // 暂时对 1/4分位数和 3/4分位数 处理
  let fourOne = []
  let fourThree = []
  let medianNumber1 = []
  let medianNumber2 = []
  for (let i = 0; i < seriesData.length; i++) {
    if (seriesData[i].name === '采购价1/4分位数') {
      fourOne.push(seriesData[i])
    }
    if (seriesData[i].name === '采购价中位数') {
      medianNumber1.push(seriesData[i])
    }
    if (seriesData[i].name === '销售价中位数') {
      medianNumber2.push(seriesData[i])
    }
    if (seriesData[i].name === '采购价3/4分位数') {
      fourThree.push(seriesData[i])
    }
  }
  let min = Math.min(...fourOne.flatMap((item) => item.data))
  let fourThreeMax = Math.max(...fourThree.flatMap((item) => item.data))
  let medianNumber1Max = Math.max(...medianNumber1.flatMap((item) => item.data))
  let medianNumber2Max = Math.max(...medianNumber2.flatMap((item) => item.data))
  let max = null
  if (medianNumber1Max > medianNumber2Max) {
    if (fourThreeMax > medianNumber1Max) {
      max = fourThreeMax
    } else {
      max = medianNumber1Max
    }
  } else {
    if (fourThreeMax > medianNumber2Max) {
      max = fourThreeMax
    } else {
      max = medianNumber2Max
    }
  }
  // 暂时对 1/4分位数和 3/4分位数 处理

  // let min = Math.min(...seriesData.flatMap(item => item.data))
  // let max = Math.max(...seriesData.flatMap(item => item.data))

  const minValueLength = Math.abs(Math.floor(Math.log10(min)) + 1)
  const subtractValue = Math.pow(10, minValueLength - 1)

  if (min - subtractValue < 0) {
    min = 0
  } else {
    min -= subtractValue
  }

  max += subtractValue

  min = parseFloat(min.toFixed(2))
  max = parseFloat(max.toFixed(2))
  Object.assign(
    chartConfig,
    industryDataLineChart(legendData, legendSelected, timeData, seriesData, min, max),
  )
  console.log(chartConfig.chart, 'chartConfig.chart')
  //  暂时去除 平均数
  // Object.assign(echartsData,
  // smonthVal.value = returnInfo.itemInfo.smonthVal
  smonthValList.length = 0
  returnInfo.smonthValList.forEach((item) => {
    smonthValList.push({ value: item, text: item })
  })

  for (let i = 0; i < smonthValList.length; i++) {
    if (returnInfo.itemInfo.smonthVal === smonthValList[i]) {
      smonthDefaultIndex.value = i
      break
    }
  }

  incomePriceIntervalAll.length = 0
  Object.assign(incomePriceIntervalAll, returnInfo.incomePriceInterval)
  salePriceIntervalAll.length = 0
  Object.assign(salePriceIntervalAll, returnInfo.salePriceInterval)

  incomePriceInterval.length = 0
  if (
    Object.prototype.hasOwnProperty.call(
      returnInfo.incomePriceInterval,
      returnInfo.itemInfo.smonthVal,
    )
  ) {
    Object.assign(
      incomePriceInterval,
      returnInfo.incomePriceInterval[returnInfo.itemInfo.smonthVal],
    )
    incomePriceIntervalMin.value = incomePriceInterval[0].min
    incomePriceIntervalMax.value = incomePriceInterval[0].max
  } else {
    console.log(`键 ${returnInfo.itemInfo.smonthVal.value} 未找到！`)
  }

  salePriceInterval.length = 0
  // if ( res.salePriceInterval.hasOwnProperty(this.smonthVal))
  if (
    Object.prototype.hasOwnProperty.call(
      returnInfo.salePriceInterval,
      returnInfo.itemInfo.smonthVal,
    )
  ) {
    Object.assign(salePriceInterval, returnInfo.salePriceInterval[returnInfo.itemInfo.smonthVal])
    salePriceIntervalMin.value = salePriceInterval[0].min
    salePriceIntervalMax.value = salePriceInterval[0].max
  } else {
    console.log(`键 ${returnInfo.itemInfo.smonthVal} 未找到！`)
  }
  //  暂时去除 平均数

  for (let i = 0; i < incomePriceInterval.length; i++) {
    if (incomePriceInterval[i].className === 'average') {
      incomePriceInterval.splice(i, 1)
    }
  }

  for (let i = 0; i < salePriceInterval.length; i++) {
    if (salePriceInterval[i].className === 'average') {
      salePriceInterval.splice(i, 1)
    }
  }

  for (let i = 0; i < priceTrendBottonList.length; i++) {
    if (priceTrendBottonList[i].name === '平均数') {
      priceTrendBottonList.splice(i, 1)
    }
  }
  // } catch (error) {}
}

// 弹出层相关
const customFieldName = {
  text: 'value',
  value: 'id',
}
const vanPickerBoolean = ref(false)
const vanPickerTitle = ref('')
const vanPickerColumns = ref([])
const vanPickerDefaultIndex = ref(0)

// 业态和区域选择方法
const vanPickeMethod = (type) => {
  if (type === 'businessForma') {
    vanPickerTitle.value = '选择业态'
    vanPickerColumns.value = returnInfo.bizTypeList
    vanPickerDefaultIndex.value = vanPickerColumns.value.findIndex(
      (item) => item.value === returnInfo.bizType,
    )
  } else if (type === 'area') {
    vanPickerTitle.value = '选择区域'
    vanPickerColumns.value = returnInfo.regionTypeList
    vanPickerDefaultIndex.value = vanPickerColumns.value.findIndex(
      (item) => item.value === returnInfo.regionType,
    )
  }
  nextTick(() => {
    vanPickerBoolean.value = true
  })
}

// 确认选择
const vanPickerOnConfirm = (value) => {
  if (vanPickerTitle.value === '选择业态') {
    returnInfo.bizType = value.selectedOptions[0].value
    bizTypeId.value = value.selectedOptions[0].id
    initData(bizTypeId, regionTypeId)
  } else if (vanPickerTitle.value === '选择区域') {
    returnInfo.regionType = value.selectedOptions[0].value
    regionTypeId.value = value.selectedOptions[0].id
    initData(bizTypeId, regionTypeId)
  }
  vanPickerOnCancel()
}

// 取消选择
const vanPickerOnCancel = () => {
  vanPickerBoolean.value = false
}

// 月份选择
const vanPickerTimeBoolean = ref(false)
// const smonthVal = ref('')
const smonthDefaultIndex = ref(0)
const smonthValList = reactive([])
// 月份选择方法
const vanPickerTimeMethod = () => {
  for (let i = 0; i < smonthValList.length; i++) {
    if (returnInfo.itemInfo.smonthVal === smonthValList[i]) {
      smonthDefaultIndex.value = i
      break
    }
  }
  vanPickerTimeBoolean.value = true
}

// 确认月份选择
const vanPickerTimeOnConfirm = (value) => {
  returnInfo.itemInfo.smonthVal = value.selectedOptions[0].value

  incomePriceInterval.length = 0
  if (
    Object.prototype.hasOwnProperty.call(
      returnInfo.incomePriceInterval,
      returnInfo.itemInfo.smonthVal,
    )
  ) {
    Object.assign(
      incomePriceInterval,
      returnInfo.incomePriceInterval[returnInfo.itemInfo.smonthVal],
    )
    incomePriceIntervalMin.value = incomePriceInterval[0].min
    incomePriceIntervalMax.value = incomePriceInterval[0].max
  } else {
    console.log(`键 ${returnInfo.itemInfo.smonthVal} 未找到！`)
  }

  salePriceInterval.length = 0
  // if ( res.salePriceInterval.hasOwnProperty(this.smonthVal))
  if (
    Object.prototype.hasOwnProperty.call(
      returnInfo.salePriceInterval,
      returnInfo.itemInfo.smonthVal,
    )
  ) {
    Object.assign(salePriceInterval, returnInfo.salePriceInterval[returnInfo.itemInfo.smonthVal])
    salePriceIntervalMin.value = salePriceInterval[0].min
    salePriceIntervalMax.value = salePriceInterval[0].max
  } else {
    console.log(`键 ${returnInfo.itemInfo.smonthVal} 未找到！`)
  }
  //  暂时去除 平均数

  for (let i = 0; i < incomePriceInterval.length; i++) {
    if (incomePriceInterval[i].className === 'average') {
      incomePriceInterval.splice(i, 1)
    }
  }

  for (let i = 0; i < salePriceInterval.length; i++) {
    if (salePriceInterval[i].className === 'average') {
      salePriceInterval.splice(i, 1)
    }
  }
  vanPickerTimeOnCancel()
}
// 取消月份选择
const vanPickerTimeOnCancel = () => {
  vanPickerTimeBoolean.value = false
}

onMounted(() => {
  document.title = '行业数据'
  console.log(route.query, 'HomeView route.query')
  if (route.query.t && route.query.o && route.query.b) {
    sessionStorage.setItem('token', route.query.t)

    orgId.value = route.query.o

    barcode.value = route.query.b
    getBarcode()
  } else {
    console.log('跳转携带参数缺失')
  }

  // const path = window.location.href
  // console.log(path, 'path')

  // if (
  //   path.includes('sqToken') &&
  //   path.includes('orgId') &&
  //   path.includes('appCode') &&
  //   path.includes('barcode') &&
  //   path.includes('bizTypeId') &&
  //   path.includes('regionTypeId') &&
  //   path.includes('smonth')
  // ) {
  //   sqToken.value = path.split('?sqToken=')[1].split('&orgId=')[0]
  //   sessionStorage.setItem('token', sqToken.value)
  //   orgId.value = path.split('&orgId=')[1].split('&appCode=')[0]

  //   appCode.value = path.split('&appCode=')[1].split('&barcode=')[0]

  //   barcode.value = path.split('&barcode=')[1].split('&bizTypeId=')[0]

  //   bizTypeId.value = path.split('&bizTypeId=')[1].split('&regionTypeId=')[0]

  //   regionTypeId.value = path.split('&regionTypeId=')[1].split('&smonth=')[0]
  //   if (path.indexOf('&fType=')) {
  //     smonth.value = path.split('&smonth=')[1].split('&fType=')[0]
  //     fType.value = path.split('&fType=')[1]
  //   } else {
  //     smonth.value = path.split('&smonth=')[1]
  //   }
  //   getBarcode()
  //   // initLogin()
  // } else {
  //   console.log('跳转携带参数缺失')
  // }
  // // 初始化数据
  // // 这里可以添加获取数据的方法
})

onBeforeUnmount(() => {
  // 清理工作
})
</script>

<template>
  <div id="Industry_data">
    <!-- <demo></demo> -->
    <div class="product_information">
      <div class="img_box">
        <img :src="returnInfo?.itemInfo.itemImgUrl" />
      </div>
      <div class="product_information_box">
        <div class="information">
          {{ returnInfo.itemInfo.itemName }}
        </div>
        <div class="barcode">条码：{{ returnInfo.itemInfo.barCode }}</div>
      </div>
    </div>

    <div>
      <div class="price_trend">
        <div class="title_box">
          <div class="title">价格趋势</div>
          <div class="operate">
            <div class="filter_box" @click="vanPickeMethod('businessForma')">
              <div class="text_box">{{ returnInfo.bizType }}</div>
              <div class="filter_box_icon"></div>
            </div>
            <div class="filter_box" @click="vanPickeMethod('area')">
              <div class="text_box">{{ returnInfo.regionType }}</div>
              <div class="filter_box_icon"></div>
            </div>
          </div>
        </div>
        <div class="describe_box">
          注释：价格趋势采用分位数展示，例如有100个样本数据，从小到大排列第25个是1/4分位数，第50个是中位数，第75个是3/4分位数。
        </div>
        <div class="price_trend_botton">
          <div class="button_box">
            <span>采购价</span>
            <button
              v-for="(item, index) in priceTrendBottonList.slice(0, 3)"
              :key="index"
              :style="{ backgroundColor: item.selected ? item.color : '#A9A9A9' }"
              @click="echartsLegendMethod(item)"
            >
              {{ item.name }}
            </button>
          </div>
          <div class="button_box">
            <span>销售价</span>
            <button
              v-for="(item, index) in priceTrendBottonList.slice(-3)"
              :key="index"
              :style="{ backgroundColor: item.selected ? item.color : '#A9A9A9' }"
              @click="echartsLegendMethod(item)"
            >
              {{ item.name }}
            </button>
          </div>
        </div>
        <echartsDemo
          ref="chartExample"
          :echartsData="chartConfig.chart"
          @update:legendSelected="handleLegendUpdate"
          v-if="JSON.stringify(chartConfig) !== '{}'"
        ></echartsDemo>
      </div>

      <div class="range_price">
        <div class="title_box">
          <div class="title">区间价格</div>
          <div class="operate">
            <div class="filter_box" @click="vanPickerTimeMethod">
              <div class="text_box">{{ returnInfo.itemInfo.smonthVal }}</div>
              <div class="filter_box_icon"></div>
            </div>
            <!-- <div @click="vanPickerTimeBoolean = true"> {{ smonthVal }}</div> -->
          </div>
        </div>
        <!-- <div class="describe_box">注释：价格条两端代表最低价和最高价。</div> -->
        <div class="describe_box"></div>
        <div class="button_list">
          <van-button
            :type="item.buttonType"
            :style="{ backgroundColor: item.state ? item.vanButtonClass : '' }"
            v-for="(item, index) in incomePriceInterval"
            :key="index"
            size="small"
            @click="operateMethod(item, index)"
          >
            {{ item.text }}
          </van-button>
        </div>
        <div class="containers">
          <span class="title_start">采购价</span>
          <div class="graphic_ontent">
            <!-- <span class="min">¥{{ incomePriceIntervalMin }}</span>
          <span class="max">¥{{ incomePriceIntervalMax }}</span> -->
            <div v-for="(item, index) in incomePriceInterval" :key="index">
              <div
                class="bar"
                :class="item.className"
                v-if="item.state"
                :style="{ width: item.percentage }"
              >
                <div class="label">
                  <div class="labelBox" :style="{ backgroundColor: item.vanButtonClass }">
                    ¥{{ item.num }}
                    <div class="triangle" :style="{ borderColor: item.triangleClass }"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <span class="title_end"></span>
        </div>
        <div class="containers">
          <span class="title_start">销售价</span>
          <div class="graphic_ontent">
            <!-- <span class="min">¥{{ salePriceIntervalMin }}</span>
          <span class="max">¥{{ salePriceIntervalMax }}</span> -->
            <div v-for="(item, index) in salePriceInterval" :key="index">
              <div
                class="bar"
                :class="item.className"
                v-if="item.state"
                :style="{ width: item.percentage }"
              >
                <div class="label">
                  <div class="labelBox" :style="{ backgroundColor: item.vanButtonClass }">
                    ¥{{ item.num }}
                    <div class="triangle" :style="{ borderColor: item.triangleClass }"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <span class="title_end"></span>
        </div>
      </div>
    </div>
    <van-popup v-model:show="vanPickerBoolean" round position="bottom">
      <van-picker
        :title="vanPickerTitle"
        show-toolbar
        value-key="value"
        :columns="vanPickerColumns"
        :default-index="vanPickerDefaultIndex"
        :columns-field-names="customFieldName"
        @confirm="vanPickerOnConfirm"
        @cancel="vanPickerOnCancel"
      />
    </van-popup>
    <van-popup v-model:show="vanPickerTimeBoolean" round position="bottom">
      <van-picker
        title="选择月份"
        :default-index="smonthDefaultIndex"
        show-toolbar
        :columns="smonthValList"
        @confirm="vanPickerTimeOnConfirm"
        @cancel="vanPickerTimeOnCancel"
      />
    </van-popup>
  </div>
</template>

<style scoped lang="less">
#Industry_data {
  padding: 1rem 0.5rem 0 0.5rem;
  background-color: #f2f3f5;
  // height: 100%;
  overflow: hidden;

  .product_information {
    display: flex;
    justify-content: flex-start;
    margin: 0 0 0.25rem 0;
    padding: 0.5rem;
    border-radius: 10px;
    background-color: #fff;
    color: #333;

    .img_box {
      width: 6rem;
      height: 6rem;
      margin: 0 1rem 0 0;
      // flex: 1;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .product_information_box {
      font-size: 0.88rem;
      font-weight: bold;
      flex: 2;

      .information {
        overflow: hidden;
        /*将对象作为弹性伸缩盒子模型显示*/
        display: -webkit-box;
        /*设置子元素排列方式*/
        -webkit-box-orient: vertical;
        /*设置显示的行数，多出的部分会显示为...*/
        -webkit-line-clamp: 2;
        margin-bottom: 0.9rem;
      }
    }
  }

  .price_trend {
    padding: 0.5rem 0.5rem 1.55rem 0.5rem;
    // margin: 0 0 1.5rem 0;
    // border-radius: 10px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    background-color: #fff;
    color: #333;

    .title_box {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        width: 30%;
        font-weight: bold;
        font-size: 0.88rem;
        color: #333;
      }

      .operate {
        width: 70%;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        // padding: 0 0 0 0.5rem;

        .filter_box {
          padding: 0 0.5rem 0 0.5rem;
          display: flex;
          justify-content: flex-start;
          align-items: center;

          .text_box {
            margin: 0 0.3125rem 0 0;
          }

          .filter_box_icon {
            float: left;
            border-width: 0.3125rem 0.3125rem 0;
            border-style: solid;
            border-color: black transparent;
          }
        }

        .filter_box:after {
          content: '';
          display: block;
          clear: both;
        }
      }
    }

    .describe_box {
      margin: 0.25rem 0.5rem 0.6rem 0.5rem;
      color: #6d7278;
      font-size: 0.75rem;
    }

    .price_trend_botton {
      margin: 1rem 0;

      .button_box {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;
        margin: 0.5rem 0.5rem;

        span {
          display: inline-block;
          width: 15%;
          color: #333;
        }

        button {
          border: none;
          color: #fff;
          padding: 0.1rem 0.3rem;
          border-radius: 0.25rem;
          font-size: 0.75rem;
        }
      }
    }
  }

  .range_price {
    padding: 0.5rem 0.5rem 1.5rem 0.5rem;
    // border-radius: 10px;
    // border-top-left-radius: 10px;
    // border-top-right-radius: 10px;
    background-color: #fff;

    .title_box {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        width: 50%;
        font-weight: bold;
        font-size: 0.88rem;
        color: #333;
      }

      .operate {
        width: 50%;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .filter_box {
          padding: 0 0.5rem 0 0.5rem;
          display: flex;
          justify-content: flex-start;
          align-items: center;

          .text_box {
            margin: 0 0.3125rem 0 0;
          }

          .filter_box_icon {
            float: left;
            border-width: 0.3125rem 0.3125rem 0;
            border-style: solid;
            border-color: black transparent;
          }
        }

        .filter_box:after {
          content: '';
          display: block;
          clear: both;
        }
      }
    }

    .describe_box {
      margin: 0.25rem 0.5rem 0.6rem 0.5rem;
      color: #6d7278;
      font-size: 0.75rem;
    }

    .button_list {
      display: flex;
      justify-content: space-around;
      margin-bottom: 1.25rem;
      :deep(.van-button) {
        border: none;
        height: 1.5rem;
        line-height: 1.5rem;
        border-radius: 0.25rem;
        color: #fff;
      }

      :deep(.van-button--default) {
        background-color: #a9a9a9;
      }
    }

    .containers {
      padding: 1rem 0 0 0;
      display: flex;
      justify-content: space-around;
      align-items: center;
      margin: 0 0 3rem 0;

      .title_start {
        text-align: center;
        width: 20%;
        color: #333;
      }

      .title_end {
        width: 10%;
      }

      .graphic_ontent {
        display: flex;
        height: 0.8rem;
        position: relative;
        width: 100%;
        background: linear-gradient(90deg, #29b6f6 0%, #8400ff 100%);
        position: relative;
        z-index: 10;

        span.min {
          position: absolute;
          bottom: -1.25rem;
          font-weight: bold;
          color: #333;
        }

        span.max {
          position: absolute;
          bottom: -1.25rem;
          right: 0%;
          font-weight: bold;
          color: #333;
        }
      }

      .bar {
        color: #333;
        font-weight: bold;
        height: 0.8rem;
      }

      .label {
        position: absolute;
        top: -1.5rem;
        width: 100%;
        left: 50%;
        text-align: center;
        border-radius: 0.25rem;
        font-size: 0.75rem;

        .labelBox {
          border-radius: 0.25rem;
          display: inline-block;
          padding: 0 0.625rem;
          color: #fff;
          position: relative;

          .triangle {
            position: absolute;
            bottom: -0.3rem;
            left: 50%;
            margin-left: -0.3125rem;
            border-width: 0.3125rem 0.3125rem 0;
            border-style: solid;
            border-color: #333 transparent;
          }
        }
      }

      .low {
        position: absolute;
        // border-right: 1px solid #333;
      }

      .medium {
        position: absolute;
        // border-right: 1px solid #333;
        z-index: 100;
      }

      .average {
        position: absolute;
        // border-right: 1px solid #333;

        .label {
          position: absolute;
          top: 1.1rem;

          .labelBox {
            border-radius: 0.25rem;
            display: inline-block;
            padding: 0 0.625rem;
            position: relative;

            .triangle {
              position: absolute;
              bottom: 1.1rem;
              left: 50%;
              margin-left: -5px;
              border-width: 0 5px 5px;
              border-style: solid;
              // border-color: #333 transparent;
            }
          }
        }
      }

      //  暂时去除 平均数
      .low {
        position: absolute;
        // border-right: 1px solid #333;
        // 暂时对 1/4分位数和 3/4分位数 处理
        left: -1.5rem;

        // 暂时对 1/4分位数和 3/4分位数 处理
        .label {
          position: absolute;
          top: 1.1rem;

          .labelBox {
            border-radius: 0.25rem;
            display: inline-block;
            padding: 0 0.625rem;
            position: relative;

            .triangle {
              position: absolute;
              bottom: 1.1rem;
              left: 50%;
              margin-left: -5px;
              border-width: 0 5px 5px;
              border-style: solid;
              // border-color: #333 transparent;
            }
          }
        }
      }

      .high {
        position: absolute;
        // border-right: 1px solid #333;

        .label {
          position: absolute;
          top: 1.1rem;

          .labelBox {
            border-radius: 0.25rem;
            display: inline-block;
            padding: 0 0.625rem;
            position: relative;

            .triangle {
              position: absolute;
              bottom: 1.1rem;
              left: 50%;
              margin-left: -5px;
              border-width: 0 5px 5px;
              border-style: solid;
              // border-color: #333 transparent;
            }
          }
        }
      }

      //  暂时去除 平均数
      .high {
        position: absolute;
        // border-right: 1px solid #333;
      }
    }
  }

  :deep(.van-empty) {
    background-color: #fff;
  }
}
</style>
