<script setup>
import { h, ref, reactive, onBeforeMount, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getShopTree } from '@/api/reportApi'


const route = useRoute()
const router = useRouter()
//门店列表
const shopsTreeData = ref([])
const showShopsTree = ref(false)
// 树形控件展示（必须，但不展示）
const treeValue = ref()
// 树控件选中的值
const treeCheckedArr = ref([])
//树形控件默认参数
const defaultProps = ref({
  children: 'children',
  label: 'name',
})
const fullPercent = ref(100)

//获取门店Tree数据
const getShopData = async () => {
  let data = {
    head: {
      accessToken: sessionStorage.getItem('token'),
    },
  }
  const res = await getShopTree(data)
  shopsTreeData.value = res
}

import { onUnmounted, watchEffect } from 'vue';
import * as echarts from 'echarts/core';
import { RadarChart } from 'echarts/charts';
import { LegendComponent, TooltipComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// ECharts modules registration
echarts.use([RadarChart, LegendComponent, TooltipComponent, CanvasRenderer]);

// 1. MOCK DATA (Pure JavaScript)
// ===============================================
const radarData = ref({
  indicators: [
    { name: '客单价', max: 100 },
    { name: '转化率', max: 100 },
    { name: '满意度', max: 100 },
    { name: '流失率', max: 100 },
    { name: '复购率', max: 100 },
    { name: '活跃度', max: 100 },
  ],
  series: [
    { name: '当前门店', value: [75, 85, 90, 40, 60, 80] },
    { name: '企业中位数', value: [65, 70, 75, 55, 75, 70] },
  ],
});

const metrics = ref([
  { id: 'm1', title: '销售额达成率', value: '80%', rank: 32, change: 2, trend: 'up', score: 80, scoreChange: 0 },
  { id: 'm2', title: '客流', value: '23%', rank: 12, change: 5, trend: 'up', score: 94, scoreChange: -2 },
  { id: 'm3', title: '毛利率', value: '5%', rank: 78, change: 2, trend: 'down', score: 76, scoreChange: -3 },
  { id: 'm4', title: '商品异常', value: '10%', rank: 78, change: 2, trend: 'down', score: 75, scoreChange: 0 },
  { id: 'm5', title: '客诉率', value: '20%', rank: 78, change: 2, trend: 'down', score: 75, scoreChange: -2 },
  { id: 'm6', title: '客单价', value: '3.44%', rank: 78, change: 2, trend: 'down', score: 75, scoreChange: -3 },
]);

const suggestions = ref([
  { id: 'r1', type: 'warning', title: '商品异常', description: '识别到店内商品异常，请及时处理，避免影响销售。' },
  { id: 'r2', type: 'info', title: '流失率偏高', description: '近期客户流失率高于平均水平，建议增加会员活动。' }
]);

// 2. ECHARTS LOGIC
// ===============================================
const chartEl = ref(null);
let chartInstance = null;

const generateRadarOptions = (data) => {
  return {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(44, 44, 46, 0.9)',
      borderColor: '#48484a',
      textStyle: { color: '#ffffff' },
      padding: 10,
    },
    legend: {
      data: data.series.map(s => s.name),
      bottom: 10,
      left: 'center',
      icon: 'rect',
      itemWidth: 12,
      itemHeight: 12,
      textStyle: { color: '#8e8e93' },
    },
    radar: {
      indicator: data.indicators,
      shape: 'polygon',
      center: ['50%', '50%'],
      radius: '65%',
      axisName: { color: '#CCCCCC', fontSize: 14 },
      splitArea: { show: false },
      axisLine: { lineStyle: { color: '#48484a' } },
      splitLine: { lineStyle: { color: '#48484a' } },
    },
    series: data.series.map((s, index) => ({
      name: '预算 vs 开销（Budget vs spending）',
      type: 'radar',
      data: [{ value: s.value, name: s.name }],
      symbol: 'none',
      lineStyle: { width: 2, color: index === 0 ? '#5873ff' : '#34c759' },
      areaStyle: { color: index === 0 ? 'rgba(88, 115, 255, 0.3)' : 'rgba(52, 199, 89, 0.2)' },
    })),
  };
};

const initChart = () => {
    if (chartEl.value) {
        chartInstance = echarts.init(chartEl.value);
        const options = generateRadarOptions(radarData.value);
        chartInstance.setOption(options);
    }
};

const resizeChart = () => {
  chartInstance?.resize();
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', resizeChart);
});

onUnmounted(() => {
  window.removeEventListener('resize', resizeChart);
  chartInstance?.dispose();
});

watchEffect(() => {
    if (radarData.value && chartInstance) {
        const options = generateRadarOptions(radarData.value);
        chartInstance.setOption(options);
    }
});


onMounted(() => {
  getShopData()
})
onBeforeMount(() => {
  // 组件挂载前执行，此时模板已编译但尚未挂载到DOM
  document.title = '门店体检'
  if (JSON.stringify(route.query) !== '{}') {
    if (
      typeof route.query.t !== 'undefined' &&
      typeof route.query.u !== 'undefined'
    ) {
      sessionStorage.setItem('token', route.query.t)
      sessionStorage.setItem('userId', route.query.u)
    }
  }
})
</script>

<template>
  <div class="dashboard-root-container">
    <!-- Header Section -->
    <header class="dashboard-header">
      <h1 class="header-title">北京朝阳门店</h1>
      <a href="#" class="header-link">切换门店 &gt;</a>
    </header>

    <!-- Main Layout Grid -->
    <div class="dashboard-layout">
      <!-- Main Content Area (Left Column) -->
      <div class="main-content">
        <!-- Radar Chart Card -->
        <div class="card-wrapper">
          <h2 class="card-title">能力雷达图</h2>
          <div ref="chartEl" class="radar-chart-container"></div>
        </div>

        <!-- Recommendations Card -->
        <div class="card-wrapper">
          <h2 class="card-title">待改进</h2>
          <div class="rec-list">
            <div v-for="rec in suggestions" :key="rec.id" class="rec-item">
              <span class="rec-icon">{{ rec.type === 'warning' ? '⚠️' : '💡' }}</span>
              <div class="rec-content">
                <h3 class="rec-item-title">{{ rec.title }}</h3>
                <p class="rec-item-desc">{{ rec.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Sidebar Area (Right Column) -->
      <aside class="sidebar">
        <div class="card-wrapper">
          <h2 class="card-title">指标详情</h2>
          <div class="metrics-container">
            <!-- Metric Card Component Logic -->
            <div v-for="metric in metrics" :key="metric.id" class="metric-card">
              <div class="metric-header">
                <span class="metric-title">{{ metric.title }}</span>
                <span class="metric-rank">排名: {{ metric.rank }}</span>
              </div>
              <div class="metric-main-value">
                <span class="metric-value">{{ metric.value }}</span>
                <span v-if="metric.change !== 0" class="metric-change" :class="metric.trend === 'up' ? 'trend-up' : 'trend-down'">
                  <span class="icon">{{ metric.trend === 'up' ? '▲' : '▼' }}</span> {{ metric.change }}
                </span>
              </div>
              <div class="metric-score-details">
                <div class="progress-bar-container">
                  <div class="progress-bar" :style="{ width: metric.score + '%' }"></div>
                </div>
                <span class="score-text">{{ metric.score }}分</span>
                <span class="score-change" :class="{ 'trend-up': metric.scoreChange > 0, 'trend-down': metric.scoreChange < 0 }">
                  同比 {{ metric.scoreChange > 0 ? '+' : '' }}{{ metric.scoreChange }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </aside>
    </div>
  </div>
</template>

<style scoped lang="less">
/* Global Variables & Base Styles - Updated Colors */
.dashboard-root-container {
  --color-background: #141414;
  --color-card: #2C2C2E;
  --color-primary-blue: #5873FF;
  --color-secondary-green: #34C759;
  --color-danger-red: #FF3B30;
  --color-text-primary: #FFFFFF;
  --color-text-secondary: #8E8E93;
  --color-border: #48484A;

  background-color: var(--color-background);
  color: var(--color-text-primary);
  padding: 1.5rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  max-width: 1400px;
  margin: 0 auto;
}

/* Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}
.header-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}
.header-link {
  color: var(--color-text-secondary);
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.3s;
}
.header-link:hover {
  color: var(--color-text-primary);
}

/* Layout */
.dashboard-layout {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 1024px) {
  .dashboard-layout {
    grid-template-columns: 3fr 2fr;
  }
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Card Styles */
.card-wrapper {
  background-color: var(--color-card);
  padding: 1.5rem;
  border-radius: 0.75rem;
  height: 100%;
}
.card-title {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
}

/* Radar Chart */
.radar-chart-container {
  width: 100%;
  height: 400px;
}
@media (min-width: 768px) {
    .radar-chart-container {
        height: 450px;
    }
}


/* Recommendations List */
.rec-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.rec-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}
.rec-icon {
  font-size: 1.25rem;
  margin-top: 0.125rem;
}
.rec-item-title {
  margin: 0;
  font-weight: 600;
}
.rec-item-desc {
  margin: 0.25rem 0 0 0;
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}

/* Metrics List */
.metrics-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* Metric Card */
.metric-card {
  padding: 1rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  transition: all 0.3s;
}
.metric-card:hover {
  border-color: #666;
}
.metric-header, .metric-main-value, .metric-score-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.metric-title {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}
.metric-rank {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}
.metric-main-value {
  margin: 0.5rem 0 0.75rem 0;
}
.metric-value {
  font-size: 1.5rem;
  font-weight: 600;
}
.metric-change {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
}
.icon {
  margin-right: 0.25rem;
}
.metric-score-details {
  gap: 0.5rem;
}
.progress-bar-container {
  flex-grow: 1;
  height: 0.25rem;
  background-color: #48484a;
  border-radius: 99px;
  overflow: hidden;
}
.progress-bar {
  height: 100%;
  background-color: var(--color-primary-blue);
  border-radius: 99px;
}
.score-text {
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
}
.score-change {
  font-size: 0.75rem;
  white-space: nowrap;
}

/* Trend Colors */
.trend-up {
  color: var(--color-secondary-green);
}
.trend-down {
  color: var(--color-danger-red);
}
.text-secondary {
  color: var(--color-text-secondary);
}
</style>
