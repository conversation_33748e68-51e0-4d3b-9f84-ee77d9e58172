<script setup>
import { cancelAllRequests } from '@/network/axios'
import { CaretBottom, Sort } from '@element-plus/icons-vue'
import { ref, reactive, onMounted, onBeforeUnmount, computed, nextTick } from 'vue' //nextTick computed
import { useRoute, useRouter } from 'vue-router'
import { useReportStore } from '@/stores/reportStore'
import {
  getDataAPIList,
  getShopTree,
  getCategoryTree,
  getGoodsSelect,
  getVenderSelect,
  reportInterpret,
  tableDownLoadToExcel,
} from '@/api/reportApi'
import echartsDemo from '@/components/echarts/echartsDemo.vue'
import {
  createBasicBarChart,
  createStackedLine<PERSON>hart,
  createBarLineMixedChart,
  createStackedBarChart,
  createPieChart,
  createRingChart,
  createNestedBarChart,
} from '@/echarts/echarts_Js/echartsJs'
import { filterSheetTheHeader, downLoad } from '@/tool/tool'

const shopsTreeRef = ref(null)
const catesTreeRef = ref(null)

const route = useRoute()
const router = useRouter()
// 保存初始参数状态
const initialParams = ref('')
const params = ref('')

const PageNum = ref(1)

const pageTotal = ref()

const CutPage = ref('')

const RowId = ref('')

const ColName = ref('')

const OdbyChr = ref('')

const OdbyType = ref(0)

const PartID = ref(1)

const Shops = ref('')

const Cates = ref('')

// const LockColName = ref('')

// const LockHead = ref('')
// 保存初始xAPPListParam状态
const initialXAPPListParam = ref(null)
const xAPPListParam = ref(null)
const xAPPListTplctr = ref(null)
const xAPPDic = reactive({})

// 保存初始compTypeGroup状态
const initialCompTypeGroup = ref(null)
const compTypeGroup = reactive({
  show: false,
  defaultV: '',
  value: '',
  paramChr: '',
  ParamName: '类型',
  list: [],
})

const filterBoxVisible = ref(false)
const toggleFilterBox = () => {
  resetFilter()
  filterBoxVisible.value = !filterBoxVisible.value
}

const hasExtraFilterItems = computed(() => {
  if (!xAPPListParam.value) return false

  // 检查是否有除了 @Shops 和 @Cates 之外的且 show 为 true 的项
  const hasExtraParams = xAPPListParam.value.some(
    (item) => item.paramChr !== '@Shops' && item.paramChr !== '@Cates' && item.show,
  )

  // 检查 compTypeGroup 是否显示
  const hasCompTypeGroup = compTypeGroup.show

  // 检查 quickFilterList 是否存在且不为空
  const hasQuickFilters = quickFilterList.length > 0

  // 任一条件满足即显示筛选按钮
  return hasExtraParams || hasCompTypeGroup || hasQuickFilters
})

const AnalysisContent = reactive({
  value: '',
  list: [],
})
// 图形展示参数
const IsShowChart = reactive({
  echartsData: [],
  value: '',
  list: [],
})
// 表格展示参数
const IsShowTable = reactive({
  value: '',
  DefaultV: '',
  columns: [],
  data: [],
  index: 0,
})

const lineEchartsRef = ref(null)
const tableRef = ref(null)

// 从 Pinia 或 sessionStorage 获取的报表数据
const reportConfigurationData = ref(null)

// 添加防抖函数
const debounce = (fn, delay) => {
  let timer = null
  return function (...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

// 添加 tableHeight 计算
const tableHeight = computed(() => {
  const navigationHeight = navigationList.length ? 50 : 0
  const paginationHeight = 50
  return `calc(100vh - ${navigationHeight + paginationHeight + 200}px)` // 200px是其他固定元素的高度
})

const getReportData = () => {
  xAPPDic.value = reportConfigurationData.value.xAPPDic
  document.title = xAPPDic.value.appName || '自定义报表'
  reportConfigurationData.value.xAPPListParam.map((item) => {
    if (params.value.indexOf(item.paramChr) < 0 && item.Is_Select !== 0) {
      if (item.paramChr == '@SDate1,@SDate2') {
        if (params.value.indexOf('@SDate1') < 0 && params.value.indexOf('@SDate2') < 0) {
          params.value += `,${item.paramChr.split(',')[0]}=[${item.DefaultV.split(',')[0]}],${item.paramChr.split(',')[1]}=[${item.DefaultV.split(',')[1]}]`
        }
      } else {
        params.value += `,${item.paramChr}=[${item.DefaultV}]`
      }
    }
  })

  console.log('xAPPListParam', reportConfigurationData.value.xAPPListParam)

  const newList = reportConfigurationData.value.xAPPListParam.map((item) => {
    const newItem = { ...item }
    if (newItem.paramChr === '@Shops' && (newItem.ParamID * 1 === 6 || newItem.ParamID * 1 === 7)) {
      if (newItem.ParamName === '门店列表') {
        newItem.show = true
        newItem.value = ''
      }
    } else if (newItem.paramChr === '@Cates') {
      if (newItem.ParamName === '类别（控件）') {
        newItem.show = true
        newItem.value = ''
      }
    } else if (newItem.paramChr === '@CompType') {
      // 将@CompType的数据添加到组中
      compTypeGroup.list.push({
        value: newItem.DefaultV,
        text: newItem.ParamName,
      })
      if (newItem.Is_Select * 1 === 1) {
        compTypeGroup.value = newItem.ParamName
        compTypeGroup.defaultV = [newItem.DefaultV]
        const paramPattern = new RegExp(`${newItem.paramChr}=\\[.*?\\]`, 'g')
        const newValue = newItem.DefaultV

        params.value = params.value.replace(paramPattern, `${newItem.paramChr}=[${newValue}]`)
      }
      compTypeGroup.show = true
      compTypeGroup.paramChr = '@CompType'

      newItem.show = false // 隐藏原始项
    } else if (newItem.paramChr === '@Venders' && newItem.Is_Widget !== 0) {
      newItem.show = true
      if (newItem.Is_Select * 1 === 1) {
        newItem.value = newItem.DefaultV
        const paramPattern = new RegExp(`${newItem.paramChr}=\\[.*?\\]`, 'g')
        const newValue = newItem.DefaultV

        params.value = params.value.replace(paramPattern, `${newItem.paramChr}=[${newValue}]`)
      } else {
        newItem.value = ''
      }
    } else if (newItem.paramChr === '@Goods' && newItem.Is_Widget * 1 !== 0) {
      newItem.show = true
      if (newItem.Is_Select * 1 === 1) {
        newItem.value = newItem.DefaultV
        const paramPattern = new RegExp(`${newItem.paramChr}=\\[.*?\\]`, 'g')
        const newValue = newItem.DefaultV

        params.value = params.value.replace(paramPattern, `${newItem.paramChr}=[${newValue}]`)
      } else {
        newItem.value = ''
      }
    } else if (
      [
        '@SDate1',
        '@SDate2',
        '@SDate3',
        '@SDate4',
        '@SYear1',
        '@SYear2',
        '@SYear3',
        '@SYear4',
        '@SMonth1',
        '@SMonth2',
        '@SMonth3',
        '@SMonth4',
        '@SWeek1',
        '@SWeek2',
        '@SWeek3',
        '@SWeek4',
      ].includes(newItem.paramChr) &&
      newItem.Is_Widget * 1 !== 0
    ) {
      newItem.show = true
    } else if (newItem.Is_Widget * 1 === 1) {
      quickFilterList.push(newItem)

      if (newItem.Is_Select * 1 === 1) {
        quickFilterIndex.value = quickFilterList.length - 1
      }
    }
    return newItem
  })
  xAPPListParam.value = newList

  // 从params.value中提取值并更新到xAPPListParam.value中
  const updateValuesFromParams = () => {
    if (!xAPPListParam.value || !params.value) return

    xAPPListParam.value.forEach((item) => {
      if (item.paramChr && params.value.includes(item.paramChr)) {
        const regex = new RegExp(`${item.paramChr}=\\[(.*?)\\]`, 'g')
        const match = regex.exec(params.value)
        if (match && match[1]) {
          item.value = match[1]
          // item.DefaultV = match[1] 暂时注释
        }
      }
    })
  }

  updateValuesFromParams()

  // 处理快速筛选条件和开始、结束日期的对应关系，避免开始日期结束日期和快速选中项不匹配
  const updateQuickFilterSelect = () => {
    // 取开始时间
    const sdata1 = xAPPListParam.value.filter(
      (e) => e.paramChr === '@SDate1' && e.ParamName === '开始时间',
    )[0]?.value
    // 取结束时间
    const sdata2 = xAPPListParam.value.filter(
      (e) => e.paramChr === '@SDate2' && e.ParamName === '结束时间',
    )[0]?.value

    if (sdata1 && sdata2) {
      for (let i = 0; i < quickFilterList.length; i++) {
        if (
          quickFilterList[i].paramChr === '@SDate1,@SDate2' &&
          quickFilterList[i].value === sdata1 + ',' + sdata2
        ) {
          quickFilterIndex.value = i
        }
      }
    }
  }
  updateQuickFilterSelect()

  initialParams.value = params.value

  // 保存初始xAPPListParam状态（深拷贝）
  initialXAPPListParam.value = JSON.parse(JSON.stringify(xAPPListParam.value))

  // 保存初始compTypeGroup状态（深拷贝）
  initialCompTypeGroup.value = JSON.parse(JSON.stringify(compTypeGroup))

  initialQuickFilterList.value = JSON.parse(JSON.stringify(quickFilterList))
  initialQuickFilterIndex.value = quickFilterIndex.value

  if (
    reportConfigurationData.value.xAPPListTplctr &&
    reportConfigurationData.value.xAPPListTplctr.length
  ) {
    xAPPListTplctr.value = reportConfigurationData.value.xAPPListTplctr

    xAPPListTplctr.value.forEach((item) => {
      if (item.attrName == 'AnalysisContent') {
        // 分析结论模块
        AnalysisContent.value = item.attrValue
      } else if (item.attrName == 'IsShowChart') {
        // 图形模块
        IsShowChart.value = item.attrValue
      } else if (item.attrName == 'IsShowTable') {
        // 图标模块
        IsShowTable.value = item.attrValue
      } else if (item.attrName == 'CutPage') {
        // 是否存在翻页
        CutPage.value = item.attrValue
      }
    })
    // 图形数据监测
    if (IsShowChart.value != 0) {
      IsShowChartChange(xAPPListTplctr.value)
    }
    // 表格数据监测
    if (IsShowTable.value != 0) {
      IsShowTableChange(xAPPListTplctr.value)
    }
  }
  if (xAPPDic.value.askKey) {
    getData(xAPPDic.value.askKey)
  }
}
// 处理图形展示参数
const IsShowChartChange = (xAPPListTplctr) => {
  let arr = []
  IsShowChart.echartsData = []
  for (let i = 0; i < xAPPListTplctr.length; i++) {
    for (let j = 1; j < 6; j++) {
      if (xAPPListTplctr[i].attrName == 'IsShowChart' + j && xAPPListTplctr[i].attrValue !== 0) {
        arr.push(xAPPListTplctr[i])
      }
    }
  }
  if (arr.length) {
    xAPPListTplctr.forEach((item) => {
      for (let k = 0; k < arr.length; k++) {
        if (item.attrName == 'Chart' + arr[k].DefaultV + 'Name') {
          IsShowChart.echartsData.push(item)
        }
      }
    })
  }
}
// 处理表格展示参数
const IsShowTableChange = (xAPPListTplctr) => {
  let arr = []

  for (let i = 0; i < xAPPListTplctr.length; i++) {
    for (let j = 1; j < 6; j++) {
      if (xAPPListTplctr[i].attrName == 'IsShowTab' + j && xAPPListTplctr[i].attrValue !== '0') {
        arr.push(xAPPListTplctr[i])
      }
    }
  }

  if (arr.length) {
    xAPPListTplctr.forEach((item) => {
      for (let k = 0; k < arr.length; k++) {
        if (item.attrName == 'Tab' + arr[k].DefaultV + 'Name') {
          tableSwitch.list.push(item)
        }
      }
    })
  }
  IsShowTable.DefaultV = tableSwitch.list[0].DefaultV
  // PartID.value = tableSwitch.list[0].DefaultV
}

const getData = async (askKey) => {
  let data = {
    askKey: askKey,
    paramChr: `@UserID=[${sessionStorage.getItem('userId')}],@PageNum=[${PageNum.value}],@ColName=[${ColName.value}],@RowID=[${RowId.value}],@OdbyChr=[${OdbyChr.value}],@OdbyType=[${OdbyType.value}],@PartID=[${PartID.value}]${params.value}`,
  }

  console.log(data.paramChr, 'paramChr')
  try {
    const res = await getDataAPIList(data)
    console.log('getData-res', res)
    if (res.r1) {
      AnalysisContent.list = res.r1
    }
    if (res.r2 && IsShowChart.echartsData.length) {
      IsShowChart.list = []
      res.r2.forEach((item) => {
        if (!item || !item.length) return // 防御性代码

        IsShowChart.echartsData.forEach((val) => {
          if (!val || !item[0] || !item[0].Z_0) return // 防御性代码

          if (item[0].Z_0.split(',')[1] == val.DefaultV) {
            let chartConfig = null
            let title = null
            let z2Val = item[0].Z_2
            switch (item[0].Z_2) {
              case '1':
                chartConfig = createBasicBarChart(item, val)
                title = val.attrValue
                break
              case '2':
                chartConfig = createStackedLineChart(item, val)
                title = val.attrValue
                break
              case '3':
                chartConfig = createBarLineMixedChart(item, val)
                title = val.attrValue
                break
              case '4':
                chartConfig = createStackedBarChart(item, val)
                title = val.attrValue
                break
              case '5':
                chartConfig = createPieChart(item, val)
                title = val.attrValue
                break
              case '7':
                chartConfig = createRingChart(item, val)
                title = val.attrValue
                break
              case '10':
                chartConfig = createNestedBarChart(item, val)
                title = val.attrValue
                break
            }
            if (chartConfig) {
              IsShowChart.list.push({
                name: `echarts_${z2Val}_instance`,
                data: chartConfig.chart,
                title: title,
              })
            }
          }
        })
      })
    }
    if (res.r3) {
      let list = JSON.parse(JSON.stringify(res.r3))
      list.forEach((item) => {
        if (item && item[0] && item[0].Z_0 && item[0].Z_0.split(',')[1] == IsShowTable.DefaultV) {
          // 更新分页信息

          if (item[0].z_2) {
            PageNum.value = parseInt(item[0].z_2)
          }
          if (item[0].z_1) {
            pageTotal.value = parseInt(item[0].z_1)
          }
          if (!ColName.value) {
            IsShowTable.columns = filterSheetTheHeader(item)
            IsShowTable.data = item
            IsShowTable.data.splice(0, 1)
            return
          } else {
            IsShowTable.columns = filterSheetTheHeader(item)
            IsShowTable.data = item
            IsShowTable.data.splice(0, 1)
            return
          }
        }
      })
      IsShowTable.columns = IsShowTable.columns.map((col) => {
        let maxLen = col.title.length
        IsShowTable.data.forEach((row) => {
          const cell = row[col.key]
          if (cell && cell.toString().length > maxLen) {
            maxLen = cell.toString().length
          }
        })
        // 限制最小宽度100px，最大宽度300px
        const minWidth = Math.min(Math.max(maxLen * 15 + 30, 100), 300) + 'px'
        const leftMinWidth = Math.min(Math.max((maxLen / 3) * 15 + 30, 100), 150) + 'px'
        return {
          ...col,
          minWidth,
          leftMinWidth,
        }
      })
    }
  } catch (error) {
    console.error('获取页面配置失败:', error)
  }
  nextTick(() => {
    tableRef.value.setScrollLeft(0)
    tableRef.value.setScrollTop(0)
    console.log('scroll to top and left')
  })
}

// 处理表格样式和功能
const handleTableData = (key, row) => {
  const value = row[key]
  const valStr = value?.toString() ?? ''
  // 1. a_0 特殊样式
  if (row.a_0 !== '' && row.a_0 !== undefined && row.a_0 !== 'undefined' && row.a_0 !== null) {
    if (key === 'a_2') {
      return {
        color: '#2d8cf0',
        cursor: 'pointer',
      }
    }
  }
  // 2. d_ 开头且包含负号（-）
  if (key.startsWith('d_') && valStr.includes('-')) {
    return {
      color: '#ed4014',
    }
  }
  // 处理数字判断
  // const num = parseFloat(value?.toString().replace(/[^0-9.-]/g, ''))
  // if (!isNaN(num) && num < 1) {
  //   return {
  //     color: 'red',
  //   }
  // }
  return {}
}

//获取门店Tree数据
const getShopData = async () => {
  let data = {
    head: {
      accessToken: sessionStorage.getItem('token'),
    },
  }
  const res = await getShopTree(data)
  shopsTreeData.value = res
}

//获取类别Tree数据
const getCategoryData = async () => {
  let data = {
    head: {
      accessToken: sessionStorage.getItem('token'),
    },
  }
  const res = await getCategoryTree(data)
  CatesTreeData.value = res
}

const customFieldName = {
  text: '',
  value: '',
}
const goodsData = ref([])
const getGoodsData = async (data, item) => {
  const res = await getGoodsSelect(data)

  goodsData.value = JSON.parse(JSON.stringify(res))
  customFieldName.text = 'goodsName'
  customFieldName.value = 'goodsId'

  controlsStatus.value = item.paramChr
  show.value = true
}

const venderData = ref([])
const getVenderData = async (data, item) => {
  const res = await getVenderSelect(data)

  venderData.value = JSON.parse(JSON.stringify(res))
  customFieldName.text = 'venderName'
  customFieldName.value = 'venderId'

  controlsStatus.value = item.paramChr
  show.value = true
}

// 控件状态参数
const controlsStatus = ref(null)
// 弹窗展示
const show = ref(false)

const keyUpControls = debounce((item) => {
  // 如果输入框为空，说明是删除操作
  if (item.value === '' || item.value === null || item.value === undefined) {
    return
  }
  let data = {
    pageNo: 1,
    pageSize: 100,
    searchParam: item.value,
  }

  if (item.paramChr === '@Goods') {
    getGoodsData(data, item)
  }
  if (item.paramChr === '@Venders') {
    getVenderData(data, item)
  }
}, 1000)

const selectControls = (item) => {
  controlsStatus.value = item.paramChr
  if (
    item.paramChr == '@SDate1' ||
    item.paramChr == '@SDate2' ||
    item.paramChr == '@SDate3' ||
    item.paramChr == '@SDate4' ||
    item.paramChr == '@SYear2'
  ) {
    const dateArr = item.value.split('-')
    currentDate.value = dateArr

    show.value = true
  }
  if (item.paramChr == '@Shops') {
    show.value = true
  }

  if (item.paramChr == '@Cates') {
    show.value = true
  }

  if (item.paramChr == '@CompType') {
    show.value = true
  }
}
// 树形控件页面展示
const treeName = ref('')
// 树形控件展示（必须，但不展示）
const treeValue = ref()
//门店列表
const shopsTreeData = ref([])
//类别列表
const CatesTreeData = ref([])
//树形控件默认参数
const defaultProps = ref({
  children: 'children',
  label: 'name',
})
// 树控件选中的值
const treeCheckedArr = ref([])
//树形控件选择操作
const treeCheckChange = (checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys) => {
  // 创建一个数组来存储所有节点的id
  const allIds = checkedKeys.checkedNodes.map((node) => node.id)

  // 过滤出符合条件的节点
  const arr = checkedKeys.checkedNodes.filter((node) => {
    // 如果节点的pid不在allIds数组中，说明这个节点没有被其他节点引用
    return !allIds.includes(node.pid)
  })

  if (arr.length > 0) {
    // 将arr中的shopId拼接成字符串
    treeName.value = arr.map((node) => node.name).join(',')
    if (controlsStatus.value === '@Shops') {
      Shops.value = arr.map((node) => node.shopId).join(',')
      const paramPattern = new RegExp(`@Shops=\\[.*?\\]`, 'g')
      params.value = params.value.replace(paramPattern, `@Shops=[${Shops.value}]`)
    }
    if (controlsStatus.value === '@Cates') {
      Cates.value = arr.map((node) => node.id).join(',')
      const paramPattern = new RegExp(`@Cates=\\[.*?\\]`, 'g')
      params.value = params.value.replace(paramPattern, `@Cates=[${Cates.value}]`)
    }
  }
}
//时间控件页面展示
const currentDate = ref([])
//时间，树形控件确定操作
const confirm = (value) => {
  show.value = false
  // 日期选择器确认
  if (
    controlsStatus.value === '@SDate1' ||
    controlsStatus.value === '@SDate2' ||
    controlsStatus.value === '@SDate3' ||
    controlsStatus.value === '@SDate4'
  ) {
    const paramPattern = new RegExp(`${controlsStatus.value}=\\[.*?\\]`, 'g')
    const newValue = currentDate.value.join('-')
    params.value = params.value.replace(paramPattern, `${controlsStatus.value}=[${newValue}]`)

    // 更新对应字段的value
    if (xAPPListParam.value) {
      const targetItem = xAPPListParam.value.find((item) => item.paramChr === controlsStatus.value)
      if (targetItem) {
        targetItem.value = newValue
      }
    }
  } else if (controlsStatus.value === '@SYear2') {
    const paramPattern = new RegExp(`${controlsStatus.value}=\\[.*?\\]`, 'g')
    const newValue = currentDate.value.join('-')
    params.value = params.value.replace(paramPattern, `${controlsStatus.value}=[${newValue}]`)
    // 更新对应字段的value
    if (xAPPListParam.value) {
      const targetItem = xAPPListParam.value.find((item) => item.paramChr === controlsStatus.value)
      if (targetItem) {
        targetItem.value = newValue
      }
    }
  } else if (controlsStatus.value === '@Shops' || controlsStatus.value === '@Cates') {
    for (let i = 0; i < xAPPListParam.value.length; i++) {
      if (controlsStatus.value === '@Shops') {
        if (xAPPListParam.value[i].paramChr === '@Shops') {
          xAPPListParam.value[i].value = treeName.value
        }
      }
      if (controlsStatus.value === '@Cates') {
        if (xAPPListParam.value[i].paramChr === '@Cates') {
          xAPPListParam.value[i].value = treeName.value
        }
      }
    }
  } else if (controlsStatus.value === '@CompType') {
    compTypeGroup.value = value.selectedOptions[0].text

    // 查找并替换 params.value 中的值
    if (params.value && controlsStatus.value) {
      if (controlsStatus.value === '@CompType') {
        const paramPattern = new RegExp(`${controlsStatus.value}=\\[.*?\\]`, 'g')
        const newValue = value.selectedOptions[0].value
        params.value = params.value.replace(paramPattern, `${controlsStatus.value}=[${newValue}]`)
      }
    }
  } else if (controlsStatus.value === '@Venders') {
    const paramPattern = new RegExp(`${controlsStatus.value}=\\[.*?\\]`, 'g')
    const newValue = value.selectedOptions[0].venderId
    params.value = params.value.replace(paramPattern, `${controlsStatus.value}=[${newValue}]`)
    if (xAPPListParam.value) {
      const targetItem = xAPPListParam.value.find((item) => item.paramChr === controlsStatus.value)
      if (targetItem) {
        targetItem.value = value.selectedOptions[0].venderName
      }
    }
  } else if (controlsStatus.value === '@Goods') {
    const paramPattern = new RegExp(`${controlsStatus.value}=\\[.*?\\]`, 'g')
    const newValue = value.selectedOptions[0].goodsId
    params.value = params.value.replace(paramPattern, `${controlsStatus.value}=[${newValue}]`)
    if (xAPPListParam.value) {
      const targetItem = xAPPListParam.value.find((item) => item.paramChr === controlsStatus.value)
      if (targetItem) {
        targetItem.value = value.selectedOptions[0].goodsName
      }
    }
  }
  if (filterBoxVisible.value === false) {
    getData(xAPPDic.value.askKey)
  }
}

const resetFilter = () => {
  // 恢复初始参数
  params.value = initialParams.value

  // 重置 xAPPListParam
  if (initialXAPPListParam.value) {
    xAPPListParam.value.forEach((item, index) => {
      if (item.show) {
        // 从初始状态恢复每个控件的值
        const initialItem = initialXAPPListParam.value.find((i) => i.paramChr === item.paramChr)
        if (initialItem) {
          item.value = initialItem.value
        }
      }
    })
  }

  // 清空tree组件的选中值
  shopsTreeRef.value?.setCheckedKeys([])
  catesTreeRef.value?.setCheckedKeys([])

  // 重置 compTypeGroup
  if (initialCompTypeGroup.value) {
    compTypeGroup.value = initialCompTypeGroup.value.value
    compTypeGroup.ParamName = initialCompTypeGroup.value.ParamName
    compTypeGroup.defaultV = initialCompTypeGroup.value.defaultV
  }
  // 重置 quickFilterList
  if (initialQuickFilterList.value.length > 0) {
    // 清空当前列表
    quickFilterList.length = 0

    // 从初始状态恢复
    initialQuickFilterList.value.forEach((item) => {
      quickFilterList.push(JSON.parse(JSON.stringify(item)))
    })

    // 恢复选中索引
    quickFilterIndex.value = initialQuickFilterIndex.value
  }
  PageNum.value = 1
  ColName.value = ''
  RowId.value = ''
  OdbyChr.value = ''
  OdbyType.value = 0
  PartID.value = 1

  navigationList.length = 0

  console.log('所有参数已重置为初始状态', params.value)
}

const searchFilter = () => {
  getData(xAPPDic.value.askKey)
  filterBoxVisible.value = false
}

// 快速筛选
const quickFilterList = reactive([])
const quickFilterIndex = ref(-1)
const initialQuickFilterList = ref([])
const initialQuickFilterIndex = ref(-1)

const quickFilterChange = (item, index) => {
  quickFilterIndex.value = index

  if (item.paramChr == '@SDate1,@SDate2') {
    const [startDate, endDate] = item.DefaultV.split(',')
    const paramPattern1 = new RegExp(`@SDate1=\\[.*?\\]`, 'g')
    const paramPattern2 = new RegExp(`@SDate2=\\[.*?\\]`, 'g')
    params.value = params.value.replace(paramPattern1, `@SDate1=[${startDate}]`)
    params.value = params.value.replace(paramPattern2, `@SDate2=[${endDate}]`)

    // TODO：就在这里处理
    xAPPListParam.value.map((e) => {
      if (e.paramChr == '@SDate1') {
        e.value = startDate
      }
      if (e.paramChr == '@SDate2') {
        e.value = endDate
      }
    })
  } else {
    const paramPattern = new RegExp(`${item.paramChr}=\\[.*?\\]`, 'g')
    params.value = params.value.replace(paramPattern, `${item.paramChr}=[${item.DefaultV}]`)
  }
  if (filterBoxVisible.value === false) {
    getData(xAPPDic.value.askKey)
  }
}

// 表格切换数据
const tableSwitch = reactive({
  index: 0,
  list: [],
})
// 表格切换
const tableSwitchChange = (item, index) => {
  tableSwitch.index = index
  IsShowTable.DefaultV = item.DefaultV
  PartID.value = item.DefaultV
  getData(xAPPDic.value.askKey)
}

// 下载表格Excel
const tableDownLoad = async () => {
  let data = {
    askKey: xAPPDic.value.askKey,
    isNew: '',
    paramChr: `@UserID=[${sessionStorage.getItem('userId')}],@PageNum=[${PageNum.value}],@ColName=[${ColName.value}],@RowID=[${RowId.value}],@OdbyChr=[${OdbyChr.value}],@OdbyType=[${OdbyType.value}],@PartID=[${PartID.value}]${params.value}`,
    title: xAPPDic.value.appName,
  }
  // let data = {
  //   head: {
  //     accessToken: sessionStorage.getItem('token'),
  //   },
  //   body: {
  //     askKey: xAPPDic.value.askKey,
  //     title: xAPPDic.value.appName,
  //     isNew: 'TRUE',
  //     paramChr: `@UserID=[${sessionStorage.getItem('userId')}],@PageNum=[${PageNum.value}],@ColName=[${ColName.value}],@RowID=[${RowId.value}],@OdbyChr=[${OdbyChr.value}],@OdbyType=[${OdbyType.value}],@PartID=[${PartID.value}]${params.value}`,
  //   },
  // }

  const res = await tableDownLoadToExcel(data)
  downLoad(res, `${xAPPDic.value.appName}.xlsx`)
}

//下钻导航
const navigationList = reactive([])
// 删除navigationList对应下标
const navigationChange = (item, index, str) => {
  if (index === navigationList.length - 1) {
    return
  }

  if (item.a_2 === '回到首页') {
    navigationList.length = 0
  } else {
    navigationList.splice(index + 1)
  }

  handleTableClick(item, str)
}

//分页操作
const pageChange = (str) => {
  if (str === 'Up') {
    PageNum.value = PageNum.value - 1
  }
  if (str === 'Down') {
    PageNum.value = PageNum.value * 1 + 1
  }
  getData(xAPPDic.value.askKey)
}

//表格下钻
const handleTableClick = (row, str) => {
  if (str === 'table') {
    if (
      row.a_0 !== '' &&
      row.a_0 !== undefined &&
      row.a_0 !== 'undefined' &&
      row.a_0 !== null &&
      row.a_0 !== 'Total'
    ) {
      RowId.value = row.a_1
      ColName.value = row.a_0

      // 创建深拷贝对象
      const homeRow = JSON.parse(JSON.stringify(row))
      // 清空所有值，但保留 a_0
      Object.keys(homeRow).forEach((key) => {
        if (key !== 'a_0') {
          homeRow[key] = ''
        }
      })
      // 设置a_2为"回到首页"
      homeRow.a_2 = '回到首页'

      // 检查navigationList是否为空，如果为空则添加homeRow
      if (navigationList.length === 0) {
        navigationList.push(homeRow)
      }

      // 添加当前行到navigationList
      navigationList.push(row)

      if (row.a_0.includes('SName') || row.a_0.includes('ShopName') || row.a_0.includes('cate_1')) {
        const paramPattern = new RegExp(`@Shops=\\[.*?\\]`, 'g')
        params.value = params.value.replace(paramPattern, `@Shops=[${row.a_1}]`)

        // 联动设置门店选择条件的值为下钻的门店id
        if (row.a_1) {
          // 设置选中的店铺值
          Shops.value = row.a_1
          // // TODO: 设置门店选择input值联动
          // xAPPListParam.value.map(i => {
          //   if(i.paramChr === '@Shops' && i.ParamName === '门店列表'){
          //     i.value = row.a_2
          //   }
          // })

          // // TODO： 设置tree选中值, 这里需要讨论交互，有冲突。
          // treeCheckedArr.value = [row.a_1]
        }
      }
      if (row.a_0.includes('CategoryName')) {
        const paramPattern = new RegExp(`@Cates=\\[.*?\\]`, 'g')
        params.value = params.value.replace(paramPattern, `@Cates=[${row.a_1}]`)
      }
      if (row.a_0.includes('GoodsName')) {
        // 检查参数中是否包含@Goods或@Shops
        if (params.value.includes('@Goods')) {
          const paramPattern = new RegExp(`@Goods=\\[.*?\\]`, 'g')
          params.value = params.value.replace(paramPattern, `@Goods=[${row.a_1}]`)
        }
        // if (params.value.includes('@Shops')) {
        //   const paramPattern = new RegExp(`@Shops=\\[.*?\\]`, 'g')
        //   params.value = params.value.replace(paramPattern, `@Shops=[${Shops.value}]`)
        // }
      }
      // if (quickFilterList.length > 0 && quickFilterList[quickFilterIndex.value].DefaultV) {
      //   const [startDate, endDate] = quickFilterList[quickFilterIndex.value].DefaultV.split(',')
      //   console.log('====================', startDate, endDate)

      //   if (params.value.includes('@SDate1')) {
      //     const paramPattern = new RegExp(`@SDate1=\\[.*?\\]`, 'g')
      //     params.value = params.value.replace(paramPattern, `@SDate1=[${startDate}]`)
      //   }
      //   if (params.value.includes('@SDate2')) {
      //     const paramPattern = new RegExp(`@SDate2=\\[.*?\\]`, 'g')
      //     params.value = params.value.replace(paramPattern, `@SDate2=[${endDate}]`)
      //   }
      // }
    } else {
      return
    }
  }
  if (str === 'navigationChange') {
    RowId.value = row.a_1
    ColName.value = row.a_2 === '回到首页' ? '' : row.a_0

    if (row.a_2 === '回到首页') {
      if (params.value.includes('@Shops')) {
        const paramPattern = new RegExp(`@Shops=\\[.*?\\]`, 'g')
        params.value = params.value.replace(paramPattern, `@Shops=[${Shops.value}]`)
      }
      if (params.value.includes('@Cates')) {
        const paramPattern = new RegExp(`@Cates=\\[.*?\\]`, 'g')
        params.value = params.value.replace(paramPattern, `@Cates=[${Cates.value}]`)
      }
      // if (params.value.includes('@Goods')) {
      //   const paramPattern = new RegExp(`@Goods=\\[.*?\\]`, 'g')
      //   params.value = params.value.replace(paramPattern, `@Goods=[${Cates.value}]`)
      // }
    }

    if (row.a_0.includes('SName') || row.a_0.includes('ShopName') || row.a_0.includes('cate_1')) {
      let paramPattern = new RegExp(`@Shops=\\[.*?\\]`, 'g')
      params.value = params.value.replace(paramPattern, `@Shops=[${row.a_1}]`)

      paramPattern = new RegExp(`@Cates=\\[.*?\\]`, 'g')
      params.value = params.value.replace(paramPattern, `@Cates=[]`)
    }
    if (row.a_0.includes('CategoryName')) {
      const paramPattern = new RegExp(`@Cates=\\[.*?\\]`, 'g')
      params.value = params.value.replace(paramPattern, `@Cates=[${row.a_1}]`)
    }
    if (row.a_0.includes('GoodsName')) {
      // 检查参数中是否包含@Goods或@Shops
      if (params.value.includes('@Goods')) {
        const paramPattern = new RegExp(`@Goods=\\[.*?\\]`, 'g')
        params.value = params.value.replace(paramPattern, `@Goods=[${row.a_1}]`)
      }
      if (params.value.includes('@Shops')) {
        const paramPattern = new RegExp(`@Shops=\\[.*?\\]`, 'g')
        params.value = params.value.replace(paramPattern, `@Shops=[${row.a_1}]`)
      }
    }

    // if (quickFilterList.length > 0 && quickFilterList[quickFilterIndex.value].DefaultV) {
    //   const [startDate, endDate] = quickFilterList[quickFilterIndex.value].DefaultV.split(',')

    //   if (params.value.includes('@SDate1')) {
    //     const paramPattern = new RegExp(`@SDate1=\\[.*?\\]`, 'g')
    //     params.value = params.value.replace(paramPattern, `@SDate1=[${startDate}]`)
    //   }
    //   if (params.value.includes('@SDate2')) {
    //     const paramPattern = new RegExp(`@SDate2=\\[.*?\\]`, 'g')
    //     params.value = params.value.replace(paramPattern, `@SDate2=[${endDate}]`)
    //   }
    // }
  }
  PageNum.value = 1
  getData(xAPPDic.value.askKey)
}
const getReportInterpret = async () => {
  let data = {
    askKey: xAPPDic.value.askKey,
    paramChr: `@UserID=[${sessionStorage.getItem('userId')}],@PageNum=[${PageNum.value}],@ColName=[${ColName.value}],@RowID=[${RowId.value}],@OdbyChr=[${OdbyChr.value}],@OdbyType=[${OdbyType.value}],@PartID=[${PartID.value}]${params.value}`,
    title: xAPPDic.value.appName,
    isNew: 'TRUE',
  }
  console.log(data.paramChr, 'paramChr')
  try {
    const res = await reportInterpret(data)
    console.log('reportInterpret', res)
    sessionStorage.setItem('reportInfo', JSON.stringify({ name: res.title, url: res.ossFilePath }))
    router.push({
      path: '/ai',
      query: {
        t: sessionStorage.getItem('token'),
        u: sessionStorage.getItem('userId'),
        a: 'report',
      },
    })
  } catch (error) {
    console.error('报表解读失败', error)
  }
}
onMounted(() => {
  // 初始化 Pinia store
  const reportStore = useReportStore()

  // 从 Pinia 或 sessionStorage 获取报表数据
  if (reportStore.reportConfigurationData) {
    reportConfigurationData.value = reportStore.reportConfigurationData
  } else {
    // 如果 Pinia 中没有数据，尝试从 sessionStorage 获取
    const sessionData = sessionStorage.getItem('reportConfigurationData')
    if (sessionData) {
      reportConfigurationData.value = JSON.parse(sessionData)
    }
  }

  getReportData()
  getShopData()
  getCategoryData()
})
onBeforeUnmount(() => {
  cancelAllRequests()
})
</script>

<template>
  <div class="customize-report">
    <div class="report-interpret" @click="getReportInterpret">
      <!-- <img alt src="@/assets/images/report/ai.png" /> -->
    </div>
    <!-- <div class="report-header">
      <h2 v-if="JSON.stringify(xAPPDic) !== '{}'">自定义报表{{ xAPPDic.value.appName }}</h2>
    </div> -->
    <div class="report-content">
      <div v-if="route.query.data">
        <h3>报表信息</h3>
        <div class="info-item">
          <span>名称：</span>
          <span>{{ JSON.parse(route.query.data).item.name }}</span>
        </div>
      </div>
      <div class="module-box module-box-first">
        <!-- <h3>控件</h3> -->
        <div class="controls-box">
          <div v-if="xAPPListParam" class="controls-item">
            <div
              v-for="(item, index) in xAPPListParam.filter(
                (item) => (item.paramChr === '@Shops' || item.paramChr === '@Cates') && item.show,
              )"
              :key="index"
            >
              <div v-if="item.show" class="controls-item-box">
                <el-input
                  v-model="item.value"
                  :placeholder="item.value ? item.value : item.ParamName"
                  readonly
                  :suffix-icon="Sort"
                  @click="selectControls(item)"
                />
                <!-- <van-field
                class="controls-item-field"
                v-model="item.value"
                readonly
                :label="item.ParamName"
                placeholder="请选择"
                @click="selectControls(item)"
              /> -->
              </div>
            </div>
            <!--<div v-if="compTypeGroup.show" class="controls-item">
              <el-input
                v-model="compTypeGroup.value"
                :placeholder="compTypeGroup.value ? compTypeGroup.value : compTypeGroup.ParamName"
                readonly
                :suffix-icon="CaretBottom"
                @click="selectControls(compTypeGroup)"
              />
               <van-field
              class="controls-item-field"
              v-model="compTypeGroup.value"
              readonly
              :label="compTypeGroup.ParamName"
              placeholder="请选择类型"
              @click="selectControls(compTypeGroup)"
            />
            </div>-->
          </div>
          <el-button v-if="hasExtraFilterItems" class="filter-button" @click="toggleFilterBox"
            ><img src="../../assets/images/report/filter.png" alt="筛选" />筛选
          </el-button>
          <!-- <van-button
            type="primary"
            size="small"
            v-if="hasExtraFilterItems"
            class="filter-button"
            @click="toggleFilterBox"
            >筛选</van-button
          > -->
        </div>
      </div>
      <div v-if="filterBoxVisible" class="filter-overlay" @click="filterBoxVisible = false"></div>
      <!-- <transition name="slide-fade"> -->
      <div class="filter-box" v-if="filterBoxVisible">
        <div class="filter-item">
          <div
            class="filter-item-box"
            v-for="(item, index) in xAPPListParam.filter(
              (item) => item.paramChr !== '@Shops' && item.paramChr !== '@Cates' && item.show,
            )"
            :key="index"
          >
            <div class="filter-item-box-item" v-if="item.show">
              <el-input
                v-model="item.value"
                :placeholder="item.value ? item.value : item.ParamName"
                :readonly="item.paramChr !== '@Goods' && item.paramChr !== '@Venders'"
                :suffix-icon="
                  item.paramChr !== '@Goods' && item.paramChr !== '@Venders' ? Sort : ''
                "
                @keyup="keyUpControls(item)"
                @click="selectControls(item)"
              />
            </div>
          </div>
          <div v-if="compTypeGroup.show" class="filter-item-box">
            <div class="filter-item-box-item">
              <el-input
                v-model="compTypeGroup.value"
                :placeholder="compTypeGroup.value ? compTypeGroup.value : compTypeGroup.ParamName"
                readonly
                :suffix-icon="Sort"
                @click="selectControls(compTypeGroup)"
              />
            </div>
          </div>
          <div class="quickFilter-box" v-if="quickFilterList.length">
            <van-button
              size="small"
              :type="quickFilterIndex === index ? 'primary' : 'default'"
              block
              v-for="(item, index) in quickFilterList"
              :key="index"
              @click="quickFilterChange(item, index)"
              >{{ item.ParamName }}
            </van-button>
          </div>
        </div>

        <div class="filter-button-box">
          <van-button size="small" @click="resetFilter">重置</van-button>
          <van-button type="primary" size="small" @click="searchFilter">确定</van-button>
        </div>
      </div>
      <!-- </transition> -->
      <!-- <div class="module-box" v-if="quickFilterList.length">
        <h3>快速筛选</h3>
        <div class="quickFilter-box">
          <van-button
            :type="quickFilterIndex === index ? 'primary' : 'default'"
            block
            v-for="(item, index) in quickFilterList"
            :key="index"
            size="small"
            @click="quickFilterChange(item, index)"
            >{{ item.ParamName }}
          </van-button>
        </div>
      </div> -->
      <div>
        <div v-for="(item, index) in IsShowChart.list" :key="index" class="card-box">
          <!-- <template #header> -->
          <div class="card-header">
            <div>
              {{ item.title }}
            </div>
          </div>
          <!-- </template> -->
          <div>
            <echartsDemo :echartsData="item.data" ref="lineEchartsRef"></echartsDemo>
          </div>
        </div>
        <div class="card-box">
          <!--v-if="item.attrName === 'IsShowPart1'"-->
          <!-- <template #header> -->
          <div class="card-header">
            <div v-if="JSON.stringify(xAPPDic) !== '{}'">
              {{ xAPPDic.value.appName }}
            </div>
            <div class="table-switch-tag-download">
              <!-- <el-tag
                  class="table-switch-tag"
                  :effect="tableSwitch.index === index ? 'dark' : 'plain'"
                  v-for="(item, index) in tableSwitch.list"
                  :key="item.DefaultV"
                  type="primary"
                  @click="tableSwitchChange(item, index)"
                  >{{ item.attrValue }}</el-tag
                > -->
              <!-- <img
                  alt
                  src="@/assets/images/download.png"
                  style="width: 20px; height: 20px"
                  @click="tableDownLoad"
                /> -->
            </div>
          </div>
          <!-- </template> -->

          <div>
            <div class="navigation-box" v-if="navigationList.length">
              <p>
                <span
                  v-for="(item, index) in navigationList"
                  :key="index"
                  @click="navigationChange(item, index, 'navigationChange')"
                  >{{ item.a_2 }}<van-icon name="arrow"
                /></span>
              </p>
            </div>
            <p>
              <el-tag
                class="table-switch-tag table-switch-tag-change"
                :effect="tableSwitch.index * 1 === index ? 'dark' : 'plain'"
                v-for="(item, index) in tableSwitch.list"
                :key="item.DefaultV"
                type="primary"
                @click="tableSwitchChange(item, index)"
                >{{ item.attrValue }}</el-tag
              >
            </p>
            <el-table
              ref="tableRef"
              :data="IsShowTable.data"
              style="width: 100%"
              :height="tableHeight"
              border
              stripe
            >
              <el-table-column
                v-for="(item, index) in IsShowTable.columns"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :fixed="index === 0 ? 'left' : false"
                :min-width="index === 0 ? item.leftMinWidth : item.minWidth"
              >
                <template #default="{ row }">
                  <span
                    :class="index === 0 ? 'left-fixed-ellipsis' : 'default-fixed-ellipsis'"
                    :style="handleTableData(item.key, row)"
                    @click="handleTableClick(row, 'table')"
                    >{{ row[item.key] }}</span
                  >
                </template>
              </el-table-column>
            </el-table>
            <div class="page-box">
              <van-button type="primary" v-if="PageNum !== 1" @click="pageChange('Up')" size="small"
                >上一页</van-button
              >
              {{ PageNum }} &nbsp; / &nbsp;{{ pageTotal }}
              <van-button
                type="primary"
                @click="pageChange('Down')"
                v-if="PageNum != pageTotal"
                size="small"
                >下一页</van-button
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <van-popup
      v-model:show="show"
      :round="controlsStatus === '@SDate1' || controlsStatus === '@SDate2'"
      position="bottom"
    >
      <div class="van-popup-box" v-if="controlsStatus === '@Shops' || controlsStatus === '@Cates'">
        <div class="operate-box">
          <span @click="show = false">取消</span>
          <span v-if="controlsStatus === '@Shops'">选择门店列表</span>
          <span v-if="controlsStatus === '@Cates'">选择类别列表</span>
          <span @click="confirm">确定</span>
        </div>
        <div class="van-popup-tree-box">
          <el-tree
            ref="shopsTreeRef"
            v-if="controlsStatus === '@Shops' && shopsTreeData.length"
            v-model="treeValue"
            :data="shopsTreeData"
            default-expand-all
            show-checkbox
            style="width: 240px"
            node-key="shopId"
            :default-checked-keys="treeCheckedArr"
            :props="defaultProps"
            @check="treeCheckChange"
          />
          <el-tree
            ref="catesTreeRef"
            v-if="controlsStatus === '@Cates' && CatesTreeData.length"
            v-model="treeValue"
            :data="CatesTreeData"
            default-expand-all
            show-checkbox
            style="width: 240px"
            :default-checked-keys="treeCheckedArr"
            :props="defaultProps"
            node-key="id"
            @check="treeCheckChange"
          />
        </div>
      </div>
      <van-date-picker
        v-model="currentDate"
        title="选择日期"
        v-if="
          controlsStatus == '@SDate1' ||
          controlsStatus == '@SDate2' ||
          controlsStatus == '@SDate3' ||
          controlsStatus == '@SDate4' ||
          controlsStatus == '@SYear2'
        "
        @cancel="show = false"
        @confirm="confirm"
        :columns-type="
          controlsStatus == '@SDate1' ||
          controlsStatus == '@SDate2' ||
          controlsStatus == '@SDate3' ||
          controlsStatus == '@SDate4'
            ? ['year', 'month', 'day']
            : controlsStatus == '@SYear2'
              ? ['year']
              : ['year']
        "
      />
      <van-picker
        v-if="controlsStatus == '@Goods' || controlsStatus == '@Venders'"
        title="请选择"
        :columns="controlsStatus == '@Goods' ? goodsData : venderData"
        @cancel="show = false"
        @confirm="confirm"
        :columns-field-names="customFieldName"
        show-toolbar
      />
      <van-picker
        v-if="controlsStatus === '@CompType'"
        title="请选择"
        :columns="compTypeGroup.list"
        @cancel="show = false"
        @confirm="confirm"
        show-toolbar
      />
    </van-popup>
  </div>
</template>

<style scoped lang="less">
.customize-report {
  padding: 20px;
  background: linear-gradient(180deg, #f8f9fc 0%, #f8faff 100%);

  .report-interpret {
    // font-size: 20px;
    // font-weight: bold;
    // color: #333;
    position: fixed;
    right: 0;
    top: 50%;
    // padding: 10px 15px;
    // border: 1px solid #e4e7ed;
    // background: #fff;
    z-index: 1000;
    // border-right: none;
    width: 112px;
    height: 112px;
    background-image: url('../../assets/images/report/ai.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    overflow: hidden;
    // img {
    //   display: block;
    //   width: 36px;
    //   height: 40px;
    //   margin: 0 auto 5px auto;
    // }
  }
  .report-header {
    margin-bottom: 20px;
    h2 {
      font-size: 24px;
      color: #333;
    }
  }

  .report-content {
    // background: #fff;
    // padding: 20px;
    // border-radius: 8px;
    // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    .info-item {
      margin: 10px 0;
      font-size: 16px;

      span:first-child {
        font-weight: bold;
        margin-right: 10px;
      }
    }
    .module-box {
      // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      // padding: 10px;
      // background: #fff;
      margin: 0 0 15px 0;
      // border-radius: 8px;
      // border: 1px solid #e4e7ed;

      .controls-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .controls-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: nowrap;
          width: 85%;
          .controls-item-box {
            width: 95%;
          }
          :deep(.el-input) {
            // border: none;
            // width: 100%;
            // margin: 0 10px 10px 0;
          }
          :deep(.el-input__wrapper) {
            background: #f0f0ff;
            border-radius: 30px;
          }
          :deep(.el-input__inner::placeholder) {
            // color: #606266;
            color: #6a5acd;
          }
          :deep(.el-input__icon) {
            color: #6a5acd;
          }
          // .controls-item-field {
          //   width: 100%;
          // }
        }
        .filter-button {
          margin: 0 10px 0 0;
          border: 2px solid #5b42d9;
          color: #5b42d9;
          img {
            width: 24x;
            height: 24px;
            margin: 0 8px 0 0;
          }
        }
      }
    }
    .module-box-first {
      position: relative;
      z-index: 101;
    }
    .filter-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 100;
    }
    .filter-box {
      position: absolute;
      top: 151px;
      left: 20px;
      right: 20px;
      background: #fff;
      padding: 15px;
      z-index: 101;
      transform-origin: top center;
      animation: dropdown 0.3s ease-out;
      overflow: hidden;
      .filter-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .filter-item-box {
          margin: 0 0 10px 0;
          width: 49%;
          .filter-item-box-item {
            width: 100%;
            border: 1px solid #eee;
          }
        }
      }

      .quickFilter-box {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        align-items: center;
        width: 100%;
        button {
          margin: 1px 5px 0 0;
          font-size: 18px;
          width: calc((100% - 20px) / 4);
        }
        button:nth-of-type(4n + 0) {
          margin-right: 0;
        }
      }
    }

    @keyframes dropdown {
      from {
        opacity: 0;
        transform: scaleY(0);
      }
      to {
        opacity: 1;
        transform: scaleY(1);
      }
    }
    @keyframes slideUp {
      from {
        opacity: 1;
        transform: translateY(0) scaleY(1);
      }
      to {
        opacity: 0;
        transform: translateY(-20px) scaleY(0);
      }
    }
    .filter-button-box {
      position: sticky;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 10px 0;
      background: #fff;
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      border-top: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
      button {
        width: 49%;
      }
    }
    .card-box {
      // margin: 0 0 15px 0;
      // box-shadow: none;
      padding: 0 14px;
      margin: 0 0 28px 0;
      // box-shadow: none;
      background: #ffffff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      border-radius: 24px;
      .card-header {
        // display: flex;
        // justify-content: space-between;
        // align-items: center;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 32px 32px 32px;
        padding: 34px 0 0 0;
        .table-switch-tag-download {
          display: flex;
          align-items: center;
          .table-switch-tag {
            margin: 0 2.5px;
            cursor: pointer;
          }
          img {
            margin: 0 0 0 5px;
          }
        }
      }
      p {
        display: flex;
        // justify-content: space-between;
        justify-content: space-around;
        align-items: center;
        flex-wrap: wrap;
        margin: 0 0 10px 0;
        .table-switch-tag-change {
          // width: 50%;
          // border-radius: 0px;
          width: 275px;
          height: 74px;
          border-radius: 13px;
          border: 2px solid #6a5acd;
          color: #6a5acd;
        }
        :deep(.el-tag--dark) {
          background: #6a5acd;
          color: #fff;
        }
        :deep(.el-tag--plain) {
          width: 275px;
          height: 74px;
          border-radius: 13px;
          background-color: #fff;
          border: 2px solid #6a5acd;
          color: #6a5acd;
        }
      }
      .navigation-box {
        background: #fff;
        padding: 10px 10px;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        margin: 0 0 15px 0;
        p {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          span {
            display: block;
            margin: 0 10px 0 0;
            color: #1576ff;
          }
        }
      }
      .left-fixed-ellipsis {
        display: -webkit-box;
        -webkit-line-clamp: 3; // 限制显示2行
        line-clamp: 3; // 限制显示2行
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: normal;
        line-height: 1.5;
        max-height: 4.5em; // 2行 * 1.5行高
      }
      .default-fixed-ellipsis {
        display: -webkit-box;
        -webkit-line-clamp: 3; // 限制显示2行
        line-clamp: 3; // 限制显示2行
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: normal;
        line-height: 1.5;
        max-height: 4.5em; // 2行 * 1.5行高
      }
      :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
        background: #eee;
      }
      :deep(.el-table--striped .el-table__body tr.el-table__row--striped.current-row td) {
        background: #f5f7fa;
      }
    }
  }
  .page-box {
    min-width: 58%;
    margin: 10px auto 0 auto;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    button {
      margin: 0 10px;
    }
  }
  .van-popup-box {
    width: 100%;
    height: 70vh;
    padding: 10px;
    overflow: hidden;
  }
  .operate-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    span:first-child {
      color: #969799;
    }
    span:nth-child(2) {
      color: black;
      font-weight: bold;
    }
    span:last-child {
      color: #1989fa;
    }
  }
  .van-popup-tree-box {
    margin: 15px 30px;
    padding: 0 0 100px 0;
    background: #fff;
    height: 100%;
    overflow: hidden;
    overflow-y: scroll;
  }
}
</style>
