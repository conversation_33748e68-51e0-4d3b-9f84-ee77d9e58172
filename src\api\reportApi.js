import axios from '@/network/axios' // 导入配置好的 axios 实例

// 获取所有菜单及报表信息
export const menuAll = async () => {
  try {
    const response = await axios.post(
      'nexora/api/r/call',
      {},
      {
        headers: {
          'nexora-r-path': '/NexoraRAPI/angela/api/rpt/menu/all',
        },
      },
    )

    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// 获取报表结构
export const rptStructure = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/rpt/structure',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// AI跳转获取报表结构
export const structureAskkey = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/rpt/structure/askkey',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

export const getRptData = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/rpt/data',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// 自定义报表
export const getDataAPIList = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/rpt/data/m',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

//获取门店Tree数据
export const getShopTree = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/rpt/dm/shop',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

//获取类别Tree数据
export const getCategoryTree = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/rpt/dm/category',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

//获取商品下拉框
export const getGoodsSelect = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/rpt/dm/goods',
      },
    })
    return response.result.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

//获取供应商下拉框
export const getVenderSelect = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/rpt/dm/vender',
      },
    })
    return response.result.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// 下载Excel
export const tableDownLoadToExcel = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call/export', data, {
      // headers: {
      //   'nexora-r-path': '/nexora/api/r/call/export',
      // },
      responseType: 'blob',
    })
    return response.data
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

export const reportInterpret = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/rpt/export/csv',
      },
      // responseType: 'blob',
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
  }
}
