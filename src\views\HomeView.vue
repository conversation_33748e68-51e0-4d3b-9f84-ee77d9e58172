<script setup>
import { cancelAllRequests } from '@/network/axios'
import { menuAll, rptStructure, structureAskkey } from '@/api/reportApi' // 导入 API 模块
import { useRoute, useRouter } from 'vue-router'
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue'
import { useReportStore } from '@/stores/reportStore'

const router = useRouter()
const route = useRoute()
const active = ref(1)
// 初始化 Pinia store
const reportStore = useReportStore()
// 用于存储当前报表类型
const reportConfigurationType = ref(null)
// 获取菜单列表数据
const list = reactive([])
const menuList = ref([])

const getMenuAll = async () => {
  try {
    const res = await menuAll()
    menuList.value = res
    active.value = sessionStorage.getItem('active') || active.value
    getData(menuList.value[active.value])
  } catch (error) {
    console.error('获取菜单列表失败:', error)
  }
}

const getData = async (item) => {
  list.length = 0
  if (item.child.length) {
    item.child.forEach((item) => {
      list.push(item)
    })
  }
}

// 菜单项点击处理函数
const itemClick = (item) => {
  getPageConfiguration(item)
}

// 获取页面配置信息
const getPageConfiguration = async (item) => {
  // 构建请求参数
  let data = {
    appUid: item.uid,
    rptId: item.id,
  }
  try {
    // 调用API获取页面配置
    const res = await rptStructure(data)

    let reportConfigurationData = {
      xAPPDic: res.xAPPDic,
      xAPPListParam: res.xAPPListParam,
      xAPPListTplctr: res.xAPPListTplctr,
    }
    // 将数据存储到 Pinia 和 sessionStorage
    reportStore.setReportConfigurationData(reportConfigurationData)

    // 根据模板类型处理不同的报表
    if (res.xAPPDic.tplUID == '40CF586F3CAA4981929284E7305145D4') {
      reportConfigurationType.value = 'standard' // 标准模板
      reportStore.setReportConfigurationType('standard')
      if (!route.query.rd) {
        router.push({
          path: '/report/standardReport',
        })
      } else {
        router.replace({
          path: '/report/standardReport',
        })
      }
      // 跳转到标准报表页面
    }
    if (res.xAPPDic.tplUID == '78A4EEDBD54A4E7293243F7401806D19') {
      reportConfigurationType.value = 'sheet' // 表格模板
      reportStore.setReportConfigurationType('sheet')
      if (!route.query.rd) {
        router.push({
          path: '/report/sheetReport',
        })
      } else {
        router.replace({
          path: '/report/sheetReport',
        })
      }
    }
    if (res.xAPPDic.tplUID == '304C388430124587890FBF279DF66CE8') {
      reportConfigurationType.value = 'customize' // 自定义模板
      reportStore.setReportConfigurationType('customize')
      if (!route.query.rd) {
        router.push({
          path: '/report/customizeReport',
        })
      } else {
        router.replace({
          path: '/report/customizeReport',
        })
      }
    }
    sessionStorage.setItem('active', active.value)
  } catch (error) {
    console.error('获取页面配置失败:', error)
  }
}

const getStructureAskkey = async (item) => {
  try {
    const res = await structureAskkey({
      askKey: item.askKey,
    })

    let reportConfigurationData = {
      xAPPDic: res.xAPPDic,
      xAPPListParam: res.xAPPListParam,
      xAPPListTplctr: res.xAPPListTplctr,
    }
    // 将数据存储到 Pinia 和 sessionStorage
    reportStore.setReportConfigurationData(reportConfigurationData)

    // 根据模板类型处理不同的报表
    if (res.xAPPDic.tplUID == '40CF586F3CAA4981929284E7305145D4') {
      reportConfigurationType.value = 'standard' // 标准模板
      reportStore.setReportConfigurationType('standard')
      if (!route.query.rd) {
        router.push({
          path: '/report/standardReport',
        })
      } else {
        router.replace({
          path: '/report/standardReport',
        })
      }
      // 跳转到标准报表页面
    }
    if (res.xAPPDic.tplUID == '78A4EEDBD54A4E7293243F7401806D19') {
      reportConfigurationType.value = 'sheet' // 表格模板
      reportStore.setReportConfigurationType('sheet')
      if (!route.query.rd) {
        router.push({
          path: '/report/sheetReport',
        })
      } else {
        router.replace({
          path: '/report/sheetReport',
        })
      }
    }
    if (res.xAPPDic.tplUID == '304C388430124587890FBF279DF66CE8') {
      reportConfigurationType.value = 'customize' // 自定义模板
      reportStore.setReportConfigurationType('customize')
      if (!route.query.rd) {
        router.push({
          path: '/report/customizeReport',
        })
      } else {
        router.replace({
          path: '/report/customizeReport',
        })
      }
    }
  } catch (error) {
    console.error('获取structureAskkey失败:', error)
  }
}

// 组件挂载时执行

onMounted(() => {
  console.log(route.query, 'HomeView route.query')

  if (route.query.t && route.query.u) {
    sessionStorage.setItem('token', route.query.t)
    sessionStorage.setItem('userId', route.query.u)

    if (!route.query.rd && !route.query.k) {
      document.title = '报表列表'
      getMenuAll()
    } else if (route.query.rd) {
      let itemData = JSON.parse(route.query.rd)
      document.title = itemData.appName
      getPageConfiguration(itemData)
    } else if (route.query.k) {
      let itemData = {
        askKey: route.query.k,
      }
      getStructureAskkey(itemData)
    }
  }
})

onBeforeUnmount(() => {
  cancelAllRequests()
})
</script>

<template>
  <div class="home-view">
    <div class="box" v-for="(item, index) in menuList" :key="index">
      <p class="box-item-title"><span class="iconfont"></span> {{ item.name }}</p>
      <div class="box-item">
        <p
          class="box-item-child"
          v-for="(items, indexs) in item.child"
          :key="indexs"
          @click="itemClick(items)"
        >
          {{ items.name }}
        </p>
      </div>
    </div>
    <!-- <div class="right-content">
      <van-sidebar v-model="active">
        <van-sidebar-item
          :title="item.name"
          v-for="(item, index) in menuList"
          :key="index"
          @click="getData(item)"
        />
      </van-sidebar>
    </div>
    <div class="left-content" :column-num="3">
      <van-grid :gutter="10">
        <van-grid-item square v-for="(item, index) in list" :key="index" @click="itemClick(item)">
          <van-image :src="item.img" class="van-image" />
          <div class="van-text">{{ item.name }}</div>
        </van-grid-item>
      </van-grid>
    </div> -->
  </div>
</template>

<style scoped lang="less">
// .home-view {
//   display: flex;
//   justify-content: flex-start;
//   .left-content {
//     width: 100%;
//     padding: 30px;
//     .van-image {
//       width: 30px;
//       height: 30px;
//     }
//     .van-text {
//       margin: 10px 0 0 0;
//     }
//   }
// }
.home-view {
  padding: 30px 24px 0 24px;
  background: linear-gradient(180deg, #ffffff 0%, #f8faff 100%);
  .box {
    margin: 0 0 16px 0;
    .box-item-title {
      display: flex;
      align-items: center;
      font-weight: 600;
      font-size: 28px;
      color: #333333;
      margin: 0 0 20px 0;
      .iconfont {
        display: inline-block;
        width: 8px;
        height: 32px;
        background: #5b43d6;
        border-radius: 4px;
        margin: 0 12px 0 0;
      }
    }
    .box-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;

      .box-item-child {
        width: 315px;
        height: 92px;
        line-height: 92px;
        // padding: 26px 0 86px 0;
        font-size: 28px;
        text-align: center;
        background: linear-gradient(135deg, #f6f5ff 0%, #eeeaff 100%);
        box-shadow: 0 4px 2.13vw rgba(91, 67, 214, 0.08);
        border-radius: 24px;
        border: 2px solid rgba(91, 67, 214, 0.1);
        margin: 0 0 24px 0;
      }
      .box-item-child:nth-child(odd) {
        margin-right: 12px;
      }
      .box-item-child:nth-child(even) {
        margin-left: 12px;
      }
    }
  }
}
</style>
