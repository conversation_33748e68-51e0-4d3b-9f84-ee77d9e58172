<script setup>
import { ref, onBeforeMount, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getShopTree } from '@/api/reportApi'
import { createRadarChart, } from '@/echarts/echarts_Js/echartsJs'
import echartsDemo from '@/components/echarts/echartsDemo.vue'
import { getShopCheckData } from '@/api/shopCheck'


const route = useRoute()
const shopsTreeRef = ref(null)
//门店列表
const shopsTreeData = ref([])
const showShopsTree = ref(false)
// 树形控件展示（必须，但不展示）
const treeValue = ref()
// 树控件选中的值
const treeCheckedArr = ref([])
//树形控件默认参数
const defaultProps = ref({
  children: 'children',
  label: 'name',
})
const currentShop = ref({
    name: '全部门店',
    shopId: '0',
  })
const radarChartData = ref({})
const radarEchartsRef = ref(null)
// 接口数据
const apiData = ref({})
// 雷达图数据
const radarData = ref({
  indicators: [
    { name: '销售额达成率', max: 100 },
    { name: '客单价', max: 100 },
    { name: '客件数', max: 100 },
    { name: '异常商品指数', max: 100 },
    { name: '毛利率', max: 100 },
    { name: '客流', max: 100 },
  ],
  series: [
    { name: '当前门店', value: [75, 85, 90, 40, 60, 80] },
    { name: '企业中位数', value: [65, 70, 75, 55, 75, 70] },
  ],
});
// 指标数据
const metricData = ref([
  {
    title: '销售额达成率',
    rank: 32,
    rankChange: 3,
    value: '80%',
    medianValue: '68%',
  },
  {
    title: '客流',
    rank: 12,
    rankChange: 0,
    value: '124',
    medianValue: '223',
  },
  {
    title: '毛利率',
    rank: 78,
    rankChange: -2,
    value: '12%',
    medianValue: '10%',
  },
])
// 建议数据
const suggesrData = ref([
  {
    tag: 'warn',
    title: '客流量下降',
    content: '客流量较上月下降20.3%，建议优化门店外部引流策略，增加社交媒体曝光度。',
  },
  {
    tag: 'pass',
    title: '客单价提升',
    content: '客单价同比提升15.2%，建议继续优化高价值商品的陈列位置，提升交叉销售。',
  },
  {
    tag: 'info',
    title: '库存优化',
    content: '当前库存周转率为3.2次/月，建议对滞销品进行促销清理，提高周转效率。当前库存周转率为3.2次/月，建议对滞销品进行促销清理，提高周转效率。当前库存周转率为3.2次/月，建议对滞销品进行促销清理，提高周转效率。',
  },
])

//获取门店Tree数据
const getShopData = async () => {
  let data = {
    head: {
      accessToken: sessionStorage.getItem('token'),
    },
  }
  const res = await getShopTree(data)
  shopsTreeData.value = res
  treeCheckedArr.value.push(res[0].shopId)
  shopsTreeRef.value.setCheckedKeys(treeCheckedArr.value, true)
  currentShop.value = res[0]
}
// 点击门店树
const treeCheckChange = (currentNode, hasCheck) => {
  treeCheckedArr.value.length = 0
  if (hasCheck) {
    treeCheckedArr.value.push(currentNode.shopId)
    shopsTreeRef.value.setCheckedKeys(treeCheckedArr.value, true)
    currentShop.value = currentNode
  } else {
    shopsTreeRef.value.setChecked(currentNode, hasCheck, false)
  }
  console.log(currentShop.value)
}
// 拉取数据
const getData = () => {
  getShopCheckData(currentShop.value.shopId).then((res) => {
    apiData.value = res
    // 处理数据
    radarData.value.indicators.length = 0
    radarData.value.series.length = 0
    radarData.value.indicators.push(
      { name: '销售额达成率', max: res.max_salevalue_finishrate },
      { name: '客单价', max: res.max_avg_customer_price },
      { name: '客件数', max: res.max_avg_num_purchases },
      { name: '异常商品指数', max: res.max_abysku_rate },
      { name: '毛利率', max: res.max_profit_rate },
      { name: '客流', max: res.max_sheetqty },
    )
    radarData.value.series.push(
      { name: '当前门店', value: [res.salevalue_finishrate, res.avg_customer_price, res.avg_num_purchases, res.abysku_rate, res.profit_rate, res.sheetqty] },
      { name: '企业中位数', value: [res.median_salevalue_finishrate, res.median_avg_customer_price, res.median_avg_num_purchases, res.median_abysku_rate, res.median_profit_rate, res.median_sheetqty] },
    )
    metricData.value.length = 0
    metricData.value.push(
      {
        title: '销售额达成率',
        rank: res.salevalue_finishrate_rank,
        rankChange: res.salevalue_finishrate_rank - res.prev_salevalue_finishrate_rank,
        value: res.salevalue_finishrate_str,
        medianValue: res.median_salevalue_finishrate_str,
      },
      {
        title: '客单价',
        rank: res.avg_customer_price_rank,
        rankChange: res.avg_customer_price_rank - res.prev_avg_customer_price_rank,
        value: res.avg_customer_price_str,
        medianValue: res.median_avg_customer_price_str,
      },
      {
        title: '客件数',
        rank: res.avg_num_purchases_rank,
        rankChange: res.avg_num_purchases_rank - res.prev_avg_num_purchases_rank,
        value: res.avg_num_purchases_str,
        medianValue: res.median_avg_num_purchases_str,
      },
      {
        title: '异常商品指数',
        rank: res.abysku_rate_rank,
        rankChange: res.abysku_rate_rank - res.prev_abysku_rate_rank,
        value: res.abysku_rate_str,
        medianValue: res.median_abysku_rate_str,
      },
      {
        title: '毛利率',
        rank: res.profit_rate_rank,
        rankChange: res.profit_rate_rank - res.prev_profit_rate_rank,
        value: res.profit_rate_str,
        medianValue: res.median_profit_rate_str,
      },
      {
        title: '客流',
        rank: res.sheetqty_rank,
        rankChange: res.sheetqty_rank - res.prev_sheetqty_rank,
        value: res.sheetqty_str,
        medianValue: res.median_sheetqty_str,
      },
    )

    // 更新数据
    let chartConfig = createRadarChart(radarData.value)
    if (chartConfig) {
      radarChartData.value = chartConfig.chart
    }
  })

}
// 切换门店确定
const confirm = () => {
  showShopsTree.value = false
  getData()
}

onMounted(() => {
  getShopData()
  getData()
})
onBeforeMount(() => {
  // 组件挂载前执行，此时模板已编译但尚未挂载到DOM
  document.title = '门店体检'
  if (JSON.stringify(route.query) !== '{}') {
    if (
      typeof route.query.t !== 'undefined' &&
      typeof route.query.u !== 'undefined'
    ) {
      sessionStorage.setItem('token', route.query.t)
      sessionStorage.setItem('userId', route.query.u)
    }
  }
})
</script>

<template>
  <div class="container">

    <div class="shop-selector">
      <div class="shop-selector-title"><van-icon name="shop-o" />{{ currentShop.name.split('#')[1] || currentShop.name }}</div>
      <div class="shop-selector-switch" @click="showShopsTree = !showShopsTree">
        切换门店
        <van-icon name="arrow" />
      </div>
    </div>

    <div class="point-card">
      <div class="title">
        <div class="text">能力雷达图</div>
      </div>
      <div class="content">
        <echartsDemo :height='`100vw`' :echartsData="radarChartData" :ref="radarEchartsRef"></echartsDemo>
      </div>
    </div>

    <div class="item-title">
      指标详情
    </div>
    <div class="metric-card" v-for="(item,index) in metricData" :key="index">
      <van-row class="metric-header">
        <van-col span="15" class="title-text"><span class="title-tag"></span><span>{{ item.title}}</span></van-col>
        <van-col span="9" class="rank-text">
          排名：{{ item.rank }}&nbsp;
          <sapn v-if="item.rankChange > 0" class="rank-up"><van-icon style="transform: scaleY(-1);" name="down" />+{{ item.rankChange }}</sapn>
          <sapn v-if="item.rankChange < 0" class="rank-down"><van-icon name="down" />{{ item.rankChange }}</sapn>
        </van-col>
      </van-row>
      <van-row class="metric-content">
        <van-col span="24">
          <van-row>
            <van-col span="20" class="title-text">{{ item.title }}</van-col>
            <van-col span="4" class="value-text">{{ item.value }}</van-col>
          </van-row>
          <van-row>
            <van-col span="20" class="title-text">企业中位数</van-col>
            <van-col span="4" class="value-text">{{ item.medianValue }}</van-col>
          </van-row>
        </van-col>
      </van-row>
    </div>

    <div class="suggest-card">
      <van-row class="suggest-header">
        <van-col span="24" class="title-text"><span class="title-tag">AI</span>灵犀建议</van-col>
      </van-row>
      <van-row class="suggest-content" v-for="(item,index) in suggesrData" :key="index">
        <van-col span="24">
          <van-row>
            <van-col span="2" class="tag">
              <van-icon v-if="item.tag == 'warn'" name="warn-o" size="20" color="#F59E0B" />
              <van-icon v-if="item.tag == 'pass'" name="passed" size="20" color="#22C55E" />
              <van-icon v-if="item.tag == 'info'" name="info-o" size="20" color="#3B82F6" />
            </van-col>
            <van-col span="22">
              <van-row>
                <van-col span="24" class="title-text">{{ item.title }}</van-col>
              </van-row>
              <van-row>
                <van-col span="24" class="value-text">{{ item.content }}</van-col>
              </van-row>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
    </div>

    <van-popup v-model:show="showShopsTree" position="bottom" :style="{ height: '100%' }">
      <div class="van-popup-box">
        <div class="operate-box">
          <span @click="showShopsTree = false">取消</span>
          <span>选择门店</span>
          <span @click="confirm">确定</span>
        </div>
        <div class="van-popup-tree-box">
          <el-tree ref="shopsTreeRef" v-if="shopsTreeData.length > 0" v-model="treeValue" :data="shopsTreeData"
            default-expand-all show-checkbox style="width: 98%" node-key="shopId"
            :default-checked-keys="treeCheckedArr" :props="defaultProps" @check-change="treeCheckChange" :check-strictly="true" />
        </div>
      </div>
    </van-popup>
  </div>


</template>

<style scoped lang="less">
.container {
  margin: 0;
  padding: 10px;
  background-color: #000;

  .shop-selector {
    margin: 1.33vw;
    margin-bottom: 5vw;
    padding: 15px 20px;
    border-radius: 8px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background-color: #252525;

    .shop-selector-title {
      font-size: 32px;
      color: #fff;
      margin-right: 1.33vw;

      i {
        font-size: 5.4vw;
        color: #5b42d9;
        margin-right: 10px;
      }
    }

    .shop-selector-switch {
      margin-left: auto;
      color: #5b42d9;
    }
  }
  .metric-card {
    display: flex-column;
    justify-content: flex-start;
    align-items: center;
    background: linear-gradient(to right, #2A2A2A, #1A1A1A);
    margin: 0 10px;
    margin-bottom: 5vw;
    padding: 20px;
    border-radius: 12px;
    .metric-header {
      margin-bottom: 24px;
      margin-left: 16px;
      margin-right: 16px;
      flex: 1;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      color: #fff;
      .title-text {
        display: flex;
        align-items: center;
        font-size: 32px;
        .title-tag {
          width: 8px;
          height: 70px;
          border-radius: 20%;
          background-color: #5B42D9;
          margin-right: 20px;
          padding: 4px;
        }
      }
      .rank-text {
        font-size: 40px;
        display: flex;
        align-items: center;
        .rank-up {
          font-size: 28px;
          color: #22C55E;
        }
        .rank-down {
          font-size: 28px;
          color: #EF4444;
        }
      }
    }
    .metric-content {
      background-color: #2A2A2A;
      border-radius: 8px;
      margin-left: 16px;
      margin-right: 16px;
      margin-bottom: 16px;
      padding-top: 10px;
      padding-bottom: 10px;
      .title-text {
        flex: 1;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 28px;
        color: #D1D5DB;
        margin-top: 10px;
        margin-left: 22px;
        margin-bottom: 10px;
      }
      .value-text {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        font-size: 32px;
        color: #fff;
        margin-top: 10px;
        margin-right: 22px;
        margin-bottom: 10px;
      }
    }
  }
  .suggest-card {
    display: flex-column;
    justify-content: flex-start;
    align-items: center;
    background: linear-gradient(to right, #2A2A2A, #1A1A1A);
    margin: 0 10px;
    margin-bottom: 5vw;
    padding: 20px;
    border-radius: 12px;
    .suggest-header {
      margin-bottom: 20px;
      flex: 1;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      color: #fff;
      .title-text {
        display: flex;
        font-size: 36px;
        .title-tag {
          width: 30px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          border-radius: 50%;
          background-color: #5B42D9;
          color: #fff;
          font-size: 24px;
          margin-right: 20px;
          padding: 10px;
        }
      }
    }
    .suggest-content {
      background-color: #2A2A2A;
      border-radius: 8px;
      margin-left: 16px;
      margin-right: 16px;
      margin-bottom: 24px;
      margin-top: 24px;
      .tag {
        margin-top: 30px;
      }
      .title-text {
        flex: 1;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 28px;
        color: #fff;
        margin-top: 26px;
        margin-left: 4px;
        margin-right: 22px;
        margin-bottom: 4px;
      }
      .value-text {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        font-size: 24px;
        color: #D1D5DB;
        margin-top: 4px;
        margin-left: 4px;
        margin-right: 22px;
        margin-bottom: 24px;
      }
    }
  }
  .item-title {
    display: flex-column;
    justify-content: flex-start;
    align-items: center;
    flex: 1;
    margin: 0 10px;
    margin-bottom: 32px;
    font-size: 36px;
    color: #fff;
  }
  .point-card {
    display: flex-column;
    justify-content: flex-start;
    align-items: center;
    background: linear-gradient(to right, #2A2A2A, #1A1A1A);
    margin: 0 10px;
    margin-bottom: 5vw;
    padding: 20px;
    border-radius: 12px;
    .title {
      flex: 1;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 30px;
      .text {
        font-size: 32px;
        color: #fff;
      }
    }
    .content {
      justify-content: flex-start;
    }
  }
}
.van-popup-box {
    width: 100%;
    height: 98%;
    padding: 10px;
    overflow: hidden;
  }
.operate-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    span:first-child {
      color: #969799;
    }
    span:nth-child(2) {
      color: black;
      font-weight: bold;
    }
    span:last-child {
      color: #1989fa;
    }
  }
.van-popup-tree-box {
  margin: 15px 30px;
  padding: 0 0 100px 0;
  background: #fff;
  height: 100%;
  overflow: hidden;
  overflow-y: scroll;
}
</style>
