<script setup lang="js">
import { CaretBottom } from '@element-plus/icons-vue'
import { showToast } from 'vant'
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { createBarDoubleLineChart, createBarSingleLineChart } from '@/echarts/echarts_Js/echartsJs'
import echartsDemo from '@/components/echarts/echartsDemo.vue'
const route = useRoute()
import { getRptData, getShopTree } from '@/api/reportApi'
import { getGoodsTargets, getGoodsFields } from '@/api/goodsInfo'
import { searchGoodsDetails } from '@/api/goodsDetails'
import {
  insertGoodsFocus,
  selectUserGoodsGroupList,
  deleteFocusGoodsFromGroup,
} from '@/api/goodsFocus'

const askKey = ref('0579E273FA3A4566BD1ACD05A9E2BCD4')

const Shops = ref('')

const Goods = ref('')

const ShopsName = ref('')
const treeName = ref('')
const treeValue = ref('')
const shopsTreeData = ref([])
const joinedGroupList = ref([])
const allGroupList = ref([])
const showGroupList = ref(false)
//树形控件默认参数
const defaultProps = ref({
  children: 'children',
  label: 'name',
})

// 格式化日期为数组格式 [year, month, day]
const formatDateToArray = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return [String(year), month, day]
}

// 获取当前日期所在周的起始日期（周一）
const minDate = (() => {
  const now = new Date()
  const day = now.getDay() || 7 // 将周日的0转换为7
  const diff = day - 1 // 计算与周一的差值
  const monday = new Date(now)
  monday.setDate(now.getDate() - diff)
  return formatDateToArray(monday)
})()

// 获取当前日期所在周的结束日期（周日）
const maxDate = (() => {
  const now = new Date()
  const day = now.getDay() || 7
  const diff = 7 - day // 计算与周日的差值
  const sunday = new Date(now)
  sunday.setDate(now.getDate() + diff)
  return formatDateToArray(sunday)
})()

const shopShow = ref(false)

const selectControls = () => {
  shopShow.value = true
}

//树形控件选择操作
const treeCheckChange = (checkedNodes, checkedKeys) => {
  // 创建一个数组来存储所有节点的id
  const allIds = checkedKeys.checkedNodes.map((node) => node.id)

  // 过滤出符合条件的节点
  const arr = checkedKeys.checkedNodes.filter((node) => {
    // 如果节点的pid不在allIds数组中，说明这个节点没有被其他节点引用
    return !allIds.includes(node.pid)
  })
  if (arr.length > 0) {
    treeName.value = arr.map((node) => node.name).join(',')
    treeValue.value = arr.map((node) => node.shopId).join(',')
  }
}

const confirmTreeData = () => {
  shopShow.value = false
  ShopsName.value = treeName.value
  Shops.value = treeValue.value

  getData(1)
  getData(2)
  getData(3)
}

const timeStart = ref('')

const timeEnd = ref('')

const goodsInfoData = reactive({})

const goodsExplainData = reactive({})

const active = ref(0)

const show = ref(false)

const vanPopupStr = ref()

const showPopup = (str) => {
  vanPopupStr.value = str
  show.value = true
}

const closePopup = () => {
  show.value = false
  vanPopupStr.value = ''
}

const explainState = ref(false)

const timeText = ref('')

// 添加当前选中的时间类型
const currentTimeType = ref('thisWeek')
// 时间按钮配置
const timeButtons = ref([
  { type: 'today', label: '今日' },
  { type: 'lastWeek', label: '上周' },
  { type: 'thisWeek', label: '本周' },
  { type: 'lastMonth', label: '上月' },
  { type: 'thisMonth', label: '本月' },
  { type: 'last30Days', label: '近30天' },
])

const customStartDate = ref('')
const customEndDate = ref('')

// 获取时间范围
const getTimeData = (type) => {
  currentTimeType.value = type
  const now = new Date()
  const today = formatDateToArray(now)
  let lastWeekMonday, lastWeekSunday, thisWeekMonday
  let lastMonthFirstDay, lastMonthLastDay, thisMonthFirstDay, thisMonthLastDay
  let last30Days

  switch (type) {
    case 'today':
      timeStart.value = today
      timeEnd.value = today
      break
    case 'lastWeek':
      lastWeekMonday = new Date(now)
      lastWeekMonday.setDate(now.getDate() - now.getDay() - 6)
      lastWeekSunday = new Date(now)
      lastWeekSunday.setDate(now.getDate() - now.getDay())
      timeStart.value = formatDateToArray(lastWeekMonday)
      timeEnd.value = formatDateToArray(lastWeekSunday)
      break
    case 'thisWeek':
      thisWeekMonday = new Date(now)
      thisWeekMonday.setDate(now.getDate() - now.getDay() + 1)
      timeStart.value = formatDateToArray(thisWeekMonday)
      timeEnd.value = today
      break
    case 'lastMonth':
      lastMonthFirstDay = new Date(now.getFullYear(), now.getMonth() - 1, 1)
      lastMonthLastDay = new Date(now.getFullYear(), now.getMonth(), 0)
      timeStart.value = formatDateToArray(lastMonthFirstDay)
      timeEnd.value = formatDateToArray(lastMonthLastDay)
      break
    case 'thisMonth':
      thisMonthFirstDay = new Date(now.getFullYear(), now.getMonth(), 1)
      thisMonthLastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0)
      timeStart.value = formatDateToArray(thisMonthFirstDay)
      timeEnd.value = formatDateToArray(thisMonthLastDay)
      break
    case 'last30Days':
      last30Days = new Date(now)
      last30Days.setDate(now.getDate() - 29)
      timeStart.value = formatDateToArray(last30Days)
      timeEnd.value = today
      break
  }
}

const showBottom = ref(false)

const showBottomStr = ref('')

const timeState = ref(false)

const switchIndexList = reactive([])

const showBottomPopup = async (str) => {
  showBottomStr.value = str

  timeState.value = false
  if (showBottomStr.value === 'time') {
    const textToTypeMap = {
      今日: 'today',
      上周: 'lastWeek',
      本周: 'thisWeek',
      上月: 'lastMonth',
      本月: 'thisMonth',
      近30天: 'last30Days',
    }

    // 检查timeText是否包含日期范围格式（包含 - 符号）
    if (timeText.value.includes('-')) {
      currentTimeType.value = 'custom'
      const [start, end] = timeText.value.split(' - ')
      customStartDate.value = start
      customEndDate.value = end
    } else {
      // 根据timeText设置对应的类型
      currentTimeType.value = textToTypeMap[timeText.value] || ''
    }
    showBottom.value = true
  }
  // 根据timeText设置默认选中状态
  if (showBottomStr.value === 'switchIndex') {
    try {
      const res = await getGoodsTargets()

      Object.assign(switchIndexList, res)
      showBottom.value = true
    } catch (error) {
      console.log(error)
    }
  }
}

const isSelectingStart = ref(false)

const showDatePicker = (isStart = true) => {
  isSelectingStart.value = isStart
  showBottomStr.value = ''
  timeState.value = true
}

// 获取当前日期
const getCurrentDate = () => {
  const now = new Date()
  return formatDateToArray(now)
}

const currentDate = ref(getCurrentDate())

const columnsType = ['year', 'month', 'day']

const dateRangeMin = (() => {
  const now = new Date()
  now.setFullYear(now.getFullYear() - 2)
  now.setMonth(0) // 设置为1月
  now.setDate(1) // 设置为1号
  return now
})()

const dateRangeMax = (() => {
  const now = new Date()
  now.setFullYear(now.getFullYear() + 2)
  now.setMonth(11) // 设置为12月
  now.setDate(31) // 设置为31号
  return now
})()

const confirmData = () => {
  if (currentTimeType.value === 'custom') {
    // 检查结束日期是否大于开始日期
    if (customEndDate.value && customStartDate.value) {
      const startDate = new Date(customStartDate.value)
      const endDate = new Date(customEndDate.value)
      if (endDate < startDate) {
        showToast('结束日期不能小于开始日期')
        return
      }
    }
    timeText.value = `${customStartDate.value || '开始日期'} - ${customEndDate.value || '结束日期'}`
  } else {
    const buttonTextMap = {
      today: '今日',
      lastWeek: '上周',
      thisWeek: '本周',
      lastMonth: '上月',
      thisMonth: '本月',
      last30Days: '近30天',
    }
    timeText.value = buttonTextMap[currentTimeType.value] || ''
  }
  showBottom.value = false
  getData(3)
}

const cancelData = () => {
  showBottom.value = false
}

const confirmTime = () => {
  currentTimeType.value = 'custom'

  if (isSelectingStart.value) {
    timeStart.value = currentDate.value
    customStartDate.value = currentDate.value.join('-')
  } else {
    timeEnd.value = currentDate.value
    customEndDate.value = currentDate.value.join('-')
  }

  showBottomStr.value = 'time'
  timeState.value = false
}

const cancelTime = () => {
  showBottomStr.value = 'time'
  timeState.value = false
}

const switchIndexListIndex = ref(0)

const switchIndexChange = async (item, index) => {
  try {
    const res = await getGoodsFields({ targetId: item.id })

    // 保留第一列门店名称
    tableColumns[0] = { prop: 'ShopName', label: '门店名称', width: 100, fixed: true }

    // 从第二列开始更新其他列
    for (let i = 0; i < res.length; i++) {
      tableColumns[i + 1] = {
        prop: res[i].fieldName,
        label: res[i].fieldDesc,
        width: 120,
        sortable: true,
      }
    }

    switchIndexListIndex.value = index
    showBottom.value = false
  } catch (error) {
    console.log(error)
  }
}

const tableData = reactive([])

// 表格列配置
const tableColumns = reactive([
  { prop: 'ShopName', label: '门店名称', width: 100, fixed: true },
  { prop: 'SheetQty', label: '今日销量', width: 110, sortable: true },
  { prop: 'Price', label: '今日销售额', width: 120, sortable: true },
  { prop: 'Qty', label: '客单数', width: 95, sortable: true },
  { prop: 'Qty_30', label: '近30天销量', width: 125, sortable: true },
  { prop: 'SaleValue_30', label: '近30天销售额', width: 140, sortable: true },
])

const saleConfig = reactive({})
const grossMarginConfig = reactive({})
const inventoryConfig = reactive({})
const costConfig = reactive({})

const getData = async (num) => {
  let data = {
    askKey: askKey.value,
    paramChr: `@UserID=[${sessionStorage.getItem('userId')}],@Shops=[${Shops.value}],@SDate1=[${timeStart.value.join('-')}],@SDate2=[${timeEnd.value.join('-')}],@Goods=[${Goods.value}],@PartID=[${num}]`,
  }
  console.log(data.paramChr, 'paramChr')
  try {
    const res = await getRptData(data)

    if (num * 1 === 1) {
      console.log(`getData-res-${num}`, res)
      let goodsInfoObj = {}
      let goodsExplainObj = []
      for (let i = 0; i < res.length; i++) {
        if (res[i].KPI === 'GoodsName') {
          goodsInfoObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'CategoryName') {
          goodsInfoObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'BarCode') {
          goodsInfoObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'GoodsID') {
          goodsInfoObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'VenderName') {
          goodsInfoObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'Spec') {
          goodsInfoObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'Price') {
          goodsInfoObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'MemPrice') {
          goodsInfoObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'CrtDate') {
          goodsInfoObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'Brand') {
          goodsInfoObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'UnitName') {
          goodsInfoObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'NewPrice') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'NewCost') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'SaleValue') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'Profit') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'CloseCostV') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'Qty') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'Profit') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'CloseQty') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'Price') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'Cost') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'SheetQty') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'AvgPrice') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'AvgCost') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'QtyDivSheet') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'SaleValue_30') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'SaleValue_60') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'LastSaleDate') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'LastRecDate') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'StockDays') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'RunType') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'Qty_30') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'Qty_60') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'LastSaleQty') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'LastRecQty') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'StkOutSKURate') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'Prom_Type') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'SaleRank') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'AbyProfit') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
        if (res[i].KPI === 'AbyNote') {
          goodsExplainObj[res[i].KPI] = res[i].KPIValue
        }
      }
      Object.assign(goodsInfoData, goodsInfoObj)
      Object.assign(goodsExplainData, goodsExplainObj)
      gethGoodsDetails()
    }
    if (num * 1 === 2) {
      console.log(`getData-res-${num}`, res)
      Object.assign(tableData, res)
    }
    if (num * 1 === 3) {
      console.log(`getData-res-${num}`, res)
      let sale = []
      let grossMargin = []
      let inventory = []
      let cost = []
      for (let i = 0; i < res.length; i++) {
        if (res[i].Type * 1 === 1) {
          sale.push(res[i])
        }
        if (res[i].Type * 1 === 2) {
          grossMargin.push(res[i])
        }
        if (res[i].Type * 1 === 3) {
          inventory.push(res[i])
        }
        if (res[i].Type * 1 === 4) {
          cost.push(res[i])
        }
      }
      const saleData = {
        xAxis: [], // X轴日期
        barData: [], // 柱状图数据（销售额）
        lineData1: [], // 折线图数据1（销量）
        lineData2: [], // 折线图数据2（客单量）
        saleValueTB: [],
        SaleValueHB: [],
        QtyTB: [],
        QtyHB: [],
        AvgSheetQty: [],
      }
      sale.forEach((item) => {
        saleData.xAxis.push(item.SDate)
        saleData.barData.push(item.SaleValue)
        saleData.lineData1.push(item.Qty)
        saleData.lineData2.push(item.SheetQty)
        saleData.saleValueTB.push(item.SaleValueTB)
        saleData.SaleValueHB.push(item.SaleValueHB)
        saleData.QtyTB.push(item.QtyTB)
        saleData.QtyHB.push(item.QtyHB)
        saleData.AvgSheetQty.push(item.AvgSheetQty)
      })
      // 更新chartConfig
      Object.assign(
        saleConfig,
        createBarDoubleLineChart(saleData, {
          barName: '销售额',
          lineName1: '销量',
          lineName2: '客单量',
        }),
      )

      const grossMarginData = {
        xAxis: [], // X轴日期
        barData: [], // 柱状图数据（毛利额）
        lineData: [], // 折线图数据（毛利率）
        ProfitTB: [],
        ProfitHB: [],
        ProfitRateTB: [],
        ProfitRateHB: [],
      }

      grossMargin.forEach((item) => {
        grossMarginData.xAxis.push(item.SDate)
        grossMarginData.barData.push(item.Profit)
        grossMarginData.lineData.push(item.ProfitRate)
        grossMarginData.ProfitTB.push(item.ProfitTB)
        grossMarginData.ProfitHB.push(item.ProfitHB)
        grossMarginData.ProfitRateTB.push(item.ProfitRateTB)
        grossMarginData.ProfitRateHB.push(item.ProfitRateHB)
      })

      Object.assign(
        grossMarginConfig,
        createBarSingleLineChart(
          grossMarginData,
          {
            barName: '毛利额',
            lineName: '毛利率',
          },
          'grossMargin',
        ),
      )

      const inventoryData = {
        xAxis: [], // X轴日期
        barData: [], // 柱状图数据（毛利额）
        lineData: [], // 折线图数据（毛利率）
        SafeCloseQty: [],
      }

      inventory.forEach((item) => {
        inventoryData.xAxis.push(item.SDate)
        inventoryData.barData.push(item.CloseValue)
        inventoryData.lineData.push(item.CloseQty)
        inventoryData.SafeCloseQty.push(item.SafeCloseQty)
      })

      Object.assign(
        inventoryConfig,
        createBarSingleLineChart(
          inventoryData,
          {
            barName: '库存金额',
            lineName: '库存数量',
          },
          'inventory',
        ),
      )

      const costData = {
        xAxis: [], // X轴日期
        barData: [], // 柱状图数据（毛利额）
        lineData: [], // 折线图数据（毛利率）
        Cost: [],
        AvgCost: [],
      }

      cost.forEach((item) => {
        costData.xAxis.push(item.SDate)
        costData.barData.push(item.CostValue)
        costData.lineData.push(item.CostOffsetRate)
        costData.Cost.push(item.Cost)
        costData.AvgCost.push(item.AvgCost)
      })

      Object.assign(
        costConfig,
        createBarSingleLineChart(
          costData,
          {
            barName: '金额',
            lineName: '进价偏移率',
          },
          'cost',
        ),
      )
    }
  } catch (error) {
    console.error('获取页面配置失败:', error)
  }
}

const gethGoodsDetails = async () => {
  let data = {
    searchCondition: goodsInfoData.BarCode,
  }
  console.log(data, 'data')

  try {
    const res = await searchGoodsDetails(data)
    console.log(res, 'res-gethGoodsDetails')
    goodsInfoData.mainHttpUrl = res.productList[0].mainHttpUrl
  } catch (error) {
    console.log(error)
  }
}

//获取门店Tree数据
const getShopData = async () => {
  let data = {
    head: {
      accessToken: sessionStorage.getItem('token'),
    },
  }
  const res = await getShopTree(data)

  shopsTreeData.value = res
}

/*侧边栏*/
const isSidebarOpen = ref(false)

import joinTheGroup from '@/assets/images/goodsInfo/joinTheGroup.png'

const sidebarList = [
  {
    img: joinTheGroup,
    name: '加入组合',
    id: 1,
  },
]

const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value
}

const openSidebarInfo = (item) => {
  if (!item || !item.name) return
  // 其他逻辑...
  if (item.name === '加入组合') {
    searchGroup()
  }
}

/*侧边栏*/

const searchGroup = () => {
  selectUserGoodsGroupList(Goods.value).then((res) => {
    console.log(res, 'res-selectUserGoodsGroupList')
    joinedGroupList.value = res
    selectUserGoodsGroupList('').then((res) => {
      console.log(res, 'res-selectUserGoodsGroupList')
      allGroupList.value = res
      allGroupList.value.forEach((item) => {
        if (joinedGroupList.value.find((i) => i.groupId === item.groupId)) {
          item.joined = true
        } else {
          item.joined = false
        }
      })
      showGroupList.value = true
    })
  })
}

const joinGroup = (item, index) => {
  if (item.joined) {
    deleteFocusGoodsFromGroup({
      goodsIds: [Goods.value],
      groupId: item.groupId,
    }).then((res) => {
      console.log(res, 'res-deleteFocusGoodsFromGroup')
      allGroupList.value[index].joined = false
    })
  } else {
    insertGoodsFocus({
      goodsId: Goods.value,
      groupId: item.groupId,
    }).then((res) => {
      console.log(res, 'res-insertGoodsFocus')
      allGroupList.value[index].joined = true
    })
  }
}

onMounted(() => {
  document.title = '商运通智能助手'
  console.log(route.query, 'goodsInfo route.query')
  console.log(dateRangeMin, 'dateRangeMin')
  if (JSON.stringify(route.query) !== '{}') {
    if (
      typeof route.query.t !== 'undefined' &&
      typeof route.query.u !== 'undefined' &&
      typeof route.query.g !== 'undefined'
    ) {
      sessionStorage.setItem('token', route.query.t)
      sessionStorage.setItem('userId', route.query.u)
      Goods.value = route.query.g
      timeStart.value = minDate
      timeEnd.value = maxDate
      getTimeData('thisWeek')
      timeText.value = '本周'
      getData(1)
      getData(2)
      getData(3)
      getShopData()
    }
  }
})
</script>

<template>
  <div class="goods-info">
    <div class="el-card-box">
      <el-input
        v-model="ShopsName"
        placeholder="选择门店"
        readonly
        :suffix-icon="CaretBottom"
        @click="selectControls()"
      />
    </div>
    <div class="el-card-box">
      <div class="goods-info-box">
        <h4>{{ goodsInfoData.GoodsName }}</h4>
        <div class="goods-info-box-content">
          <div class="goods-info-item-img">
            <img :src="goodsInfoData.mainHttpUrl" alt="" />
          </div>
          <div class="goods-info-item-info">
            <p>
              <span>品类：{{ goodsInfoData.CategoryName }}</span>
            </p>
            <p>
              <span>商品条码：{{ goodsInfoData.BarCode }}</span>
              <!-- <span @click="showPopup('more')">更多</span> -->
            </p>
            <p>
              <span>商品编号: {{ goodsInfoData.GoodsID }}</span
              ><span @click="showPopup('info')">基本信息</span>
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="el-card-box">
      <div class="goods-explain-box">
        <p class="title">
          <span>商品说明</span>
          <!-- <span>icon</span> -->
        </p>
        <div class="goods-explain-box-sign">
          <van-tag class="tag-style" type="primary">{{ goodsExplainData.SaleRank }}</van-tag>
          <van-tag class="tag-style" type="success ">{{ goodsExplainData.AbyProfit }}</van-tag>
          <van-tag class="tag-style" type="warning ">{{ goodsExplainData.AbyNote }}</van-tag>
        </div>
        <div class="goods-explain-box-price">
          <div>
            <p class="price-title">零售价</p>
            <p class="price">{{ goodsExplainData.NewPrice }}</p>
          </div>
          <div>
            <!-- <p class="link">行业价格趋势</p> -->
          </div>
          <div>
            <p class="price-title">进货价</p>
            <p class="price">{{ goodsExplainData.NewCost }}</p>
          </div>
        </div>
        <div class="goods-explain-box-content">
          <div>
            <p class="content-title">今日实时销售额 (元)</p>
            <p class="content-price">{{ goodsExplainData.SaleValue }}</p>
            <p class="content-name">今日实时销量{{ goodsExplainData.Qty }}</p>
            <p class="content-title">档案零售价</p>
            <p class="content-price">{{ goodsExplainData.Price }}</p>
            <p class="content-name">平均售价{{ goodsExplainData.AvgPrice }}</p>
          </div>
          <div>
            <p class="content-title">今日实时毛利额 (元)</p>
            <p class="content-price">{{ goodsExplainData.Profit }}</p>
            <p class="content-name">毛利额{{ goodsExplainData.Profit }}</p>
            <p class="content-title">档案进价</p>
            <p class="content-price">{{ goodsExplainData.Cost }}</p>
            <p class="content-name">平均进价{{ goodsExplainData.AvgCost }}</p>
          </div>
          <div>
            <p class="content-title">库存金额 (元)</p>
            <p class="content-price">{{ goodsExplainData.CloseCostV }}</p>
            <p class="content-name">库存数量{{ goodsExplainData.CloseQty }}</p>
            <p class="content-title">今天购买人次</p>
            <p class="content-price">{{ goodsExplainData.SheetQty }}</p>
            <p class="content-name">客件数{{ goodsExplainData.QtyDivSheet }}</p>
          </div>
        </div>
        <div class="goods-explain-operate-box" v-if="explainState">
          <h5>经营参考</h5>
          <div class="operate-box">
            <div class="left-operate-box">
              <p>
                <span>近30天销售额 (元)</span> <span>{{ goodsExplainData.SaleValue_30 }}</span>
              </p>
              <p>
                <span>近60天销售额 (元)</span> <span>{{ goodsExplainData.SaleValue_60 }}</span>
              </p>
              <p>
                <span>末次销售日</span> <span>{{ goodsExplainData.LastSaleDate }}</span>
              </p>
              <p>
                <span>末次进货日</span> <span>{{ goodsExplainData.LastRecDate }}</span>
              </p>
              <p>
                <span>可销天数</span> <span>{{ goodsExplainData.StockDays }}</span>
              </p>
              <p>
                <span>经营方式</span> <span>{{ goodsExplainData.RunType }}</span>
              </p>
            </div>
            <div class="right-operate-box">
              <p>
                <span>近30天销量</span> <span>{{ goodsExplainData.Qty_30 }}</span>
              </p>
              <p>
                <span>近60天销量</span> <span>{{ goodsExplainData.Qty_60 }}</span>
              </p>
              <p>
                <span>末次销量</span> <span>{{ goodsExplainData.LastSaleQty }}</span>
              </p>
              <p>
                <span>末次进货量</span> <span>{{ goodsExplainData.LastRecQty }}</span>
              </p>
              <p>
                <span>缺货率</span>
                <span>{{ (goodsExplainData.StkOutSKURate * 100).toFixed(2) }}%</span>
              </p>
              <p>
                <span>促销类型</span> <span>{{ goodsExplainData.Prom_Type }}</span>
              </p>
            </div>
          </div>
        </div>
      </div>
      <van-button type="default" size="small" @click="explainState = !explainState">{{
        explainState ? '收起' : '展开'
      }}</van-button>
    </div>
    <div class="el-card-box">
      <div class="goods-overview-box">
        <p class="title-content">
          <span>商品概况</span>
          <span @click="showBottomPopup('time')">{{ timeText }} 切换</span>
        </p>
        <van-tabs v-model:active="active" type="card">
          <van-tab title="销售情况">
            <echartsDemo
              ref="chartExample"
              :echartsData="saleConfig.chart"
              v-if="JSON.stringify(saleConfig) !== '{}'"
            ></echartsDemo>
          </van-tab>
          <van-tab title="毛利情况">
            <echartsDemo
              ref="grossMarginChart"
              :echartsData="grossMarginConfig.chart"
              v-if="JSON.stringify(grossMarginConfig) !== '{}'"
            ></echartsDemo>
          </van-tab>
          <van-tab title="库存">
            <echartsDemo
              ref="grossMarginChart"
              :echartsData="inventoryConfig.chart"
              v-if="JSON.stringify(inventoryConfig) !== '{}'"
            ></echartsDemo
          ></van-tab>
          <van-tab title="成本">
            <echartsDemo
              ref="grossMarginChart"
              :echartsData="costConfig.chart"
              v-if="JSON.stringify(costConfig) !== '{}'"
            ></echartsDemo
          ></van-tab>
        </van-tabs>
      </div>
    </div>
    <div class="el-card-box">
      <div class="store-perate-box">
        <p class="title-content">
          <span>门店经营情况-销售</span>
          <span @click="showBottomPopup('switchIndex')">切换指标</span>
        </p>
        <el-table :data="tableData" border style="width: 100%" height="500px">
          <el-table-column
            v-for="col in tableColumns"
            :key="col.prop"
            :prop="col.prop"
            :label="col.label"
            :width="col.width"
            :fixed="col.fixed"
            :sortable="col.sortable"
          />
        </el-table>
      </div>
    </div>
    <van-popup v-model:show="show" @click-overlay="closePopup"> </van-popup>
    <!-- <van-picker title="标题" :columns="columns" @confirm="onConfirm" @cancel="onCancel" /> -->
    <div class="van-popup-box" v-if="vanPopupStr === 'more' || vanPopupStr === 'info'">
      <div class="title">{{ vanPopupStr === 'more' ? '更多条码' : '基本信息' }}</div>
      <div class="content" v-if="vanPopupStr === 'more'">
        <p><span>6666</span><span class="copy">复制</span></p>
      </div>
      <div class="content" v-if="vanPopupStr === 'info'">
        <p>
          <span>商品条码</span><span>{{ goodsInfoData.BarCode }}</span>
        </p>
        <p>
          <span>供应商名称</span><span>{{ goodsInfoData.VenderName }}</span>
        </p>
        <p>
          <span>规格:</span><span>{{ goodsInfoData.Spec }}</span>
        </p>
        <p>
          <span>零售价:</span><span>{{ goodsInfoData.Price }}</span>
        </p>
        <p>
          <span>会员价:</span><span>{{ goodsInfoData.MemPrice }}</span>
        </p>
        <p>
          <span>修改时期:</span><span>{{ goodsInfoData.CrtDate }}</span>
        </p>
        <p>
          <span>品牌:</span><span>{{ goodsInfoData.Brand }}</span>
        </p>
        <p>
          <span>单位:</span><span>{{ goodsInfoData.UnitName }}</span>
        </p>
        <p>
          <span>品类:</span><span>{{ goodsInfoData.CategoryName }}</span>
        </p>
      </div>
    </div>
    <van-popup v-model:show="showBottom" position="bottom" :style="{ height: '50%' }">
      <div>
        <div class="van-popup-bottom-box" v-if="showBottomStr === 'time'">
          <van-button
            v-for="btn in timeButtons"
            :key="btn.type"
            :type="currentTimeType === btn.type ? 'primary' : 'default'"
            @click="getTimeData(btn.type)"
            >{{ btn.label }}</van-button
          >
          <van-button
            :type="currentTimeType === 'custom' && customStartDate ? 'primary' : 'default'"
            @click="showDatePicker(true)"
            >{{ customStartDate || '开始日期' }}</van-button
          >
          <van-button
            :type="currentTimeType === 'custom' && customEndDate ? 'primary' : 'default'"
            @click="showDatePicker(false)"
            >{{ customEndDate || '结束日期' }}</van-button
          >
        </div>
        <div class="van-popup-bottom-box" v-if="showBottomStr === 'switchIndex'">
          <van-button
            @click="switchIndexChange(item, index)"
            v-for="(item, index) in switchIndexList"
            :key="index"
            :type="switchIndexListIndex === index ? 'primary' : 'default'"
            >{{ item.targetName }}</van-button
          >
        </div>
        <div class="popup-bottom-buttons" v-if="showBottomStr === 'time'">
          <van-button type="default" @click="cancelData">取消</van-button>
          <van-button type="primary" @click="confirmData">确定</van-button>
        </div>
      </div>

      <van-date-picker
        v-if="timeState"
        v-model="currentDate"
        title="选择年月"
        :columns-type="columnsType"
        :min-date="dateRangeMin"
        :max-date="dateRangeMax"
        @confirm="confirmTime"
        @cancel="cancelTime"
      />
    </van-popup>
    <van-popup v-model:show="shopShow" position="bottom">
      <div class="tree-van-popup-box">
        <div class="tree-operate-box">
          <span @click="shopShow = false">取消</span>
          <span>选择门店列表</span>
          <span @click="confirmTreeData">确定</span>
        </div>
        <div class="tree-van-popup-data-box">
          <el-tree
            v-model="treeValue"
            :data="shopsTreeData"
            default-expand-all
            show-checkbox
            style="width: 240px"
            :props="defaultProps"
            @check="treeCheckChange"
          />
        </div>
      </div>
    </van-popup>
    <van-popup v-model:show="showGroupList" position="bottom">
      <div class="tree-van-popup-box">
        <div >
          <van-nav-bar title="选择加入组合" />
        </div>
        <div class="tree-van-popup-data-box">
          <van-grid :column-num="2">
            <van-grid-item v-for="(group,index) in allGroupList" :key="group.groupId" >
              <van-button style="width: 100%;" color="#7232dd" :plain="group.joined ? false : true" type="primary" @click="joinGroup(group,index)">
                {{ group.groupName }}&nbsp;<van-icon v-if="group.joined" name="success" />
              </van-button>
            </van-grid-item>
          </van-grid>
        </div>
      </div>
    </van-popup>
    <van-sticky :offset-bottom="50" position="bottom">
    <div class="sidebar-box">
      <!-- 侧边栏 -->

      <div class="sidebar-content">
        <div
          v-show="isSidebarOpen"
          class="sidebar-content-item"
          v-for="(item, index) in sidebarList"
          :key="index"
          @click="openSidebarInfo(item)"
        >
          <div class="sidebar-content-item-icon">
            <img :src="item.img" alt="" />
          </div>
          {{ item.name }}
        </div>

        <div
          class="sidebar-content-item-button-box"
          :style="isSidebarOpen ? 'margin:0 0 0 34px' : ''"
        >
          <img src="../../assets//images//goodsInfo/leftArrow.png" alt="" v-if="isSidebarOpen" />
          <button
            @click="toggleSidebar"
            :class="[isSidebarOpen ? 'sidebar-content-item-button' : 'sidebar-box-button']"
          >
            {{ isSidebarOpen ? '收起' : '展开' }}
          </button>
        </div>
      </div>
    </div>
    </van-sticky>
  </div>
</template>

<style lang="less" scoped>
.goods-info {
  position: relative;
  padding: 20px;
  .el-card-box {
    margin: 0 0 20px 0;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    button {
      width: 100%;
      margin: 30px 0 0 0;
      border-radius: 0;
      border-left: none;
      border-right: none;
      border-bottom: none;
    }
  }
  .goods-info-box {
    padding: 30px;
    h4 {
      margin: 0 0 20px 0;
    }
    .goods-info-box-content {
      display: flex;
      justify-content: space-between;
      .goods-info-item-img {
        width: 30%;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          display: block;
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .goods-info-item-info {
        width: 65%;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
        p {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 24px;
          span:nth-child(odd) {
            color: #808080;
          }
          span:nth-child(even) {
            color: blue;
          }
        }
      }
    }
  }
  .goods-explain-box {
    padding: 30px 30px 0 30px;
    .title {
      margin: 0 0 20px 0;
      span {
        display: inline-block;
        width: 50%;
      }
      span:nth-child(odd) {
        text-align: left;
      }
      span:nth-child(even) {
        text-align: right;
      }
    }
    .goods-explain-box-sign {
      margin: 0 0 20px 0;
      display: flex;
      justify-content: space-between;
      .tag-style {
        width: 30%;
        height: 50px;
        justify-content: center;
        box-shadow: 0px 0px;
      }
      div {
        width: 30%;
        height: 50px;
        // border: 1px solid #333;
        border-radius: 10px;
        font-size: 24px;
        line-height: 50px;
        text-align: center;
        box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
      }
    }
    .goods-explain-box-price {
      padding: 20px 30px;
      margin: 0 0 20px 0;
      border: 1px solid #333;
      border-radius: 10px;
      display: flex;
      justify-content: space-between;
      div {
        width: 30%;
        position: relative; // 新增相对定位容器
        .price-title {
          text-align: center;
          padding: 5px 0;
        }
        p.price {
          text-align: center;
          padding: 5px 0;
        }
        p.link {
          text-align: center;
          font-size: 20px;
          color: blue;
          position: absolute; // 新增绝对定位
          bottom: 0; // 定位到底部
          left: 0;
          width: 100%; // 撑满容器宽度
        }
      }
    }
    .goods-explain-box-content {
      display: flex;
      justify-content: space-between;
      div {
        width: 33%;
        font-size: 22px;
        p {
          margin: 0 0 20px 0;
        }
        p.content-title {
        }
        p.content-price {
          font-size: 26px;
          font-weight: bold;
        }
        p.content-name {
          color: #808080;
        }
      }
    }
    .goods-explain-operate-box {
      h5 {
        margin: 0 0 20px 0;
      }
      .operate-box {
        display: flex;
        justify-content: space-between;
        div {
          width: 45%;
        }
        .left-operate-box,
        .right-operate-box {
          p {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            font-size: 22px;
            margin: 0 0 20px 0;
            height: 70px;
            span:nth-child(odd) {
              width: 40%;
            }
          }
        }
      }
    }
  }
  .goods-overview-box {
    padding: 30px;
    .title-content {
      margin: 0 0 20px 0;
      display: flex;
      justify-content: space-between;
    }
    :deep(.van-tabs__nav--card) {
      margin: 0;
    }
  }
  .store-perate-box {
    padding: 30px;
    .title-content {
      margin: 0 0 20px 0;
      display: flex;
      justify-content: space-between;
    }
    // h5 {
    //   margin: 0 0 20px 0;
    // }
  }
  .van-popup-box {
    position: fixed;
    top: 35%;
    left: 50%;
    transform: translate(-50%, -30%);
    background-color: #fff;
    z-index: 9000;
    width: 80%;
    border-radius: 4px;
    overflow: hidden;
    .title {
      width: 100%;
      padding: 20px;
      text-align: center;
      background-color: black;
      color: #fff;
    }
    .content {
      width: 100%;
      padding: 50px 20px 20px 20px;
      background-color: #fff;
      font-size: 24px;
      p {
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .copy {
          color: blue;
        }
      }
    }
  }
  .van-popup-bottom-box {
    padding: 30px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    button {
      width: 49%;
      margin: 0 0 20px 0;
    }
  }
  .popup-bottom-buttons {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    padding: 10px 16px;
    background-color: #fff;
    border-top: 1px solid #eee;
    button {
      width: 45%;
      margin: 0;
    }
  }
  .tree-van-popup-box {
    width: 100%;
    height: 70vh;
    padding: 10px;
    overflow: hidden;
  }
  .tree-operate-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    span:first-child {
      color: #969799;
    }
    span:nth-child(2) {
      color: black;
      font-weight: bold;
    }
    span:last-child {
      color: #1989fa;
    }
  }
  .tree-van-popup-data-box {
    margin: 15px 30px;
    padding: 0 0 100px 0;
    background: #fff;
    height: 100%;
    overflow: hidden;
    overflow-y: scroll;
  }

  .sidebar-box {
    position: absolute;
    top: 50%;
    left: 14px;
    transform: translateY(-50%);
    z-index: 100;

    display: flex;
    .sidebar-content {
      display: flex;
      align-items: center;
      height: 126px;
      // padding: 0 10px 0 24px;
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
      box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.5),
        0 8px 24px rgba(99, 102, 241, 0.3);
      border-radius: 24px;
      .sidebar-content-item {
        margin: 0 0 0 24px;
        color: #fff;
        font-size: 24px;
        .sidebar-content-item-icon {
          width: 48px;
          height: 48px;
          margin: 0 auto;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .sidebar-content-item-button-box {
        display: flex;
        align-items: center;
        display: flex;
        img {
          width: 24px;
          height: 24px;
        }
        .sidebar-content-item-button {
          width: 44px;
          height: 114px;
          background: #eef0ff;
          border-radius: 40px;
          border: none;
          color: #5b4cff;
          margin: 0 10px 0 2px;
        }
        .sidebar-box-button {
          width: 48px;
          height: 126px;
          background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
          box-shadow:
            0 4px 8px rgba(0, 0, 0, 0.5),
            0 8px 24px rgba(99, 102, 241, 0.3);
          border-radius: 24px;
          border: none;
          color: #fff;
        }
      }
    }
  }
}
</style>
