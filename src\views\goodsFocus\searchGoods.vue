<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showConfirmDialog, showSuccessToast, showFailToast, showToast } from 'vant'
import { getGoodsList, getIndustryData } from '@/api/goodsFocus'
import defaultImg from '@/assets/images/goodsFocus/default.png'
import { api } from '@/api/ai'
import wx from 'weixin-webview-jssdk'

const route = useRoute()
const router = useRouter()

const currentGroupId = ref('')
const searchHistory = ref([])
const searchName = ref('')
const goodsList = ref([])
const industryList = ref([])
const showGoodsList = ref(false)
const activeTab = ref(0)
// 点击录音按钮 开始true，停止false
const nextRecording = ref(false)
// 录音本地id
const recordLocalId = ref('')

// 加载搜索历史
const loadSearchHistory = () => {
  // 从本地存储中获取搜索历史
  const history = localStorage.getItem('searchHistory')
  if (history) {
    searchHistory.value = JSON.parse(history)
  } else {
    searchHistory.value = []
  }
}

// 添加搜索历史
const addSearchHistory = (name) => {
  if(!name) return
  // 先检查是否已经存在
  if (searchHistory.value.includes(name)) {
    // 删除原来的
    searchHistory.value.splice(searchHistory.value.indexOf(name), 1)
  }
  // 添加搜索历史
  searchHistory.value.unshift(name)
  // 保存到本地存储
  localStorage.setItem('searchHistory', JSON.stringify(searchHistory.value))
}

// 清除搜索历史
const clearHistory = () => {
  searchHistory.value = []
  localStorage.setItem('searchHistory', JSON.stringify(searchHistory.value))
}

// 搜索商品
const searchGoods = async (name) => {
  if (name)  {
    searchName.value = name
  }

  if (searchName.value == '') return

  // 添加搜索历史
  addSearchHistory(searchName.value)

  // 获取商品列表
  const res = await getGoodsList({
    type: '1',
    goods: searchName.value,
  })
  goodsList.value = res
  showGoodsList.value = true

  // goodsList.value = [
  //       {
  //           "goodsId": "102001",
  //           "goodsName": "可口可乐碳酸饮料桃子味限量版500ml 规格:500ml/瓶",
  //           "barCode": "4902102127141",
  //           "goodsImg": "https://itemall-std-img.viwor.net/images/73/540904/3/73-540904-2615012415761426.png!std150",
  //           "isSplitGoods": 0,
  //           "num": 1
  //       },
  //       {
  //           "goodsId": "102017",
  //           "goodsName": "可口可乐水动乐缤纷莓果味饮料600mlT 规格:600ml/瓶",
  //           "barCode": "6975682480508",
  //           "goodsImg": "https://itemall-std-img.viwor.net/images/73/995096/3/73-995096-0917393272205402.jpg!std150",
  //           "isSplitGoods": 0,
  //           "num": 2
  //       },
  //       {
  //           "goodsId": "102910",
  //           "goodsName": "可口可乐摩登罐330ml 规格:330ml/听",
  //           "barCode": "6928804014570",
  //           "goodsImg": "https://itemall-std-img.viwor.net/images/73/408508/3/73-408508-2118592182144338.jpg!std150",
  //           "isSplitGoods": 0,
  //           "num": 3
  //       },
  //       {
  //           "goodsId": "121553",
  //           "goodsName": "可口可乐2L+雪碧2L组合装 规格:2L*2/组",
  //           "barCode": "6928804011784",
  //           "goodsImg": "https://itemall-std-img.viwor.net/images/73/647868/3/73-647868-2820391347580865.JPG!std150",
  //           "isSplitGoods": 0,
  //           "num": 19
  //       }
  //   ]
}

// 搜索行业数据
const searchIndustry = async () => {
  if (searchName.value == '') return

  // 添加搜索历史
  addSearchHistory(searchName.value)

  // 获取行业数据
  const res = await getIndustryData({
    orgId: '123', // 固定值
    barcode: searchName.value,
  })
  industryList.value = res.itemList
  showGoodsList.value = true

  // industryList.value = [
  //       {
  //           "goodsId": "102001",
  //           "goodsName": "可口可乐碳酸饮料桃子味限量版500ml 规格:500ml/瓶",
  //           "barCode": "4902102127141",
  //           "goodsImg": "https://itemall-std-img.viwor.net/images/73/540904/3/73-540904-2615012415761426.png!std150",
  //           "isSplitGoods": 0,
  //           "num": 1
  //       },
  //       {
  //           "goodsId": "102017",
  //           "goodsName": "可口可乐水动乐缤纷莓果味饮料600mlT 规格:600ml/瓶",
  //           "barCode": "6975682480508",
  //           "goodsImg": "https://itemall-std-img.viwor.net/images/73/995096/3/73-995096-0917393272205402.jpg!std150",
  //           "isSplitGoods": 0,
  //           "num": 2
  //       },
  //   ]
}

const clearSearch = () => {
  searchName.value = ''
  showGoodsList.value = false
}

const wxConfig = () => {
  api
    .getWxJsapiSignature(location.href.split('#')[0])
    .then((res) => {
      const result = res.result
      console.log(JSON.stringify(res))
      wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: result.appId, // 必填，公众号的唯一标识
        timestamp: result.timestamp, // 必填，生成签名的时间戳
        nonceStr: result.nonceStr, // 必填，生成签名的随机串
        signature: result.signature, // 必填，签名
        jsApiList: ['startRecord', 'stopRecord', 'translateVoice'], // 必填，需要使用的JS接口列表
      })
    })
    .catch((error) => {
      console.log(error)
    })
}

// 语音输入
const audioInput = () => {
  nextRecording.value = !nextRecording.value
  // TODO：调起语音识别
  if (nextRecording.value) {
    wx.startRecord()
  } else {
    wx.stopRecord({
      success: function (res) {
        console.log(JSON.stringify(res))
        recordLocalId.value = res.localId
        wx.translateVoice({
          localId: res.localId, // 需要识别的音频的本地Id，由录音相关接口获得
          isShowProgressTips: 1, // 默认为1，显示进度提示
          success: function (res) {
            console.log('识别结果：', res)
            searchName.value = res.translateResult
          },
          fail: function (res) {
            console.log('识别失败：', JSON.stringify(res))
          },
        })
      },
      fail: function (res) {
        console.log('停止录音失败：', JSON.stringify(res))
      },
    })
  }
  // 调用搜索
  searchGoods(searchName.value)
}

// tab切换
const onClickTab = () => {
  if (activeTab.value == 0) {
    searchGoods()
  } else {
    searchIndustry()
  }
};

// tab切换前检查
const beforeChange = (index) => {
  // 返回 false 表示阻止此次切换
  // 校验searchName.value的值是不少于8位数字的全条码
  if (index === 1 && !/^\d{8,}$/.test(searchName.value)) {
    console.log(activeTab.value)
    showToast('请输入不少于8位数字的全条码')
    return false
  }
  return true
};

// 进入商品详情
const handleGoodsClick = (item) => {
  router.push({
    path: '/goodsInfo',
    query: {
      t: sessionStorage.getItem('token'),
      u: sessionStorage.getItem('userId'),
      g: item.goodsId
    }
  })
};

onMounted(() => {
  // document.title = '抄送人'
  wxConfig()
  if (JSON.stringify(route.query) !== '{}') {
    if (
      typeof route.query.t !== 'undefined' &&
      typeof route.query.u !== 'undefined'
    ) {
      sessionStorage.setItem('token', route.query.t)
      sessionStorage.setItem('userId', route.query.u)

      if(route.query.g) {
        currentGroupId.value = route.query.g
        // 用户搜索历史
        loadSearchHistory()
      } else if(route.query.b){
        searchName.value = route.query.b
        showGoodsList.value = true
        activeTab.value = 1
        searchIndustry()
      }

    }
  }
})
onBeforeUnmount(() => {
  goodsList.value = []
  industryList.value = []
})

</script>

<template>
  <div class="search">
    <div class="top-filter">
      <div class="search-container">
        <van-search
          v-model="searchName"
          show-action
          placeholder="请输入名称搜索"
          @search="searchGoods('')"
          @clear="clearSearch"
        >
          <template #right-icon>
            <van-button plain icon="/nexora-front/src/assets/images/goodsFocus/audio.png" @click="audioInput" type="primary" size="small" />
          </template>
          <template #action>
            <van-button @click="searchGoods('')" type="primary" size="small">搜索</van-button>
          </template>
        </van-search>
      </div>
    </div>
    <div class="history-box" v-if="!showGoodsList">
      <div class="title">
        <div class="title-text">搜索记录</div>
        <div class="title-icon" @click="clearHistory">
          <van-icon name="delete-o" />
        </div>
      </div>
      <div class="history-list">
        <div class="item" v-for="(item, index) in searchHistory" :key="index" @click="searchGoods(item)">
          <div class="item-text">{{ item }}</div>
        </div>
      </div>
    </div>
    <div class="goods-list" v-if="showGoodsList">
      <van-tabs v-model:active="activeTab" @click-tab="onClickTab" :before-change="beforeChange">
        <van-tab title="企业内数据">
          <div class="item" v-for="item in goodsList" :key="item.goodsId" @click="handleGoodsClick(item)">
            <div class="item-image">
              <img :src="item.goodsImg || defaultImg" alt="Product Image" style="width: 100%" />
            </div>
            <div class="item-details">
              <h4>
                <span class="name">{{ item.goodsName }}</span>
              </h4>
              <div class="item-info">
                <p>商品条码：{{ item.barCode }}</p>
                <p>商品编码：{{ item.goodsId }}</p>
              </div>
            </div>
          </div>
        </van-tab>
        <van-tab title="相关行业数据">
          <div class="item" v-for="item in industryList" :key="item.barCode" @click="handleGoodsClick(item)">
            <div class="item-image">
              <img :src="item.itemImgUrl || defaultImg" alt="Product Image" />
            </div>
            <div class="item-details">
              <h4>
                <span class="name">{{ item.itemName }}</span>
              </h4>
              <div class="item-info">
                <p>商品条码：{{ item.barCode }}</p>
                <p>商品编码：{{ item.goodsId || '' }}</p>
              </div>
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>

<style scoped lang="less">
.search{
  background-color: #ececec;

  .top-filter{}

  .history-box {
    background-color: #fff;
    margin-top: 1.67vw;

    .title {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .title-text {
        font-size: 3.87vw;
        padding: 2.33vw;
      }

      .title-icon {
        margin-left: auto;
        padding: 2.33vw;
        font-size: 4.87vw;
      }
    }

    .history-list {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      align-items: center;
      margin: 0 1.67vw;

      .item {
        display: inline-block;
        margin: 0.67vw;
        background-color: #ececec;
        padding: 0.67vw 1.33vw;
        font-size: 3.87vw;
      }
    }
  }

  .goods-list {

    .item {
      display: flex;
      align-items: center;
      background-color: #fff;
      border-bottom: 1px solid #ececec;
      padding: 1.33vw;

      .item-image {
        width: 150px;
        height: 150px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 15px 0 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .item-details {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        // justify-content: space-between;
        // padding: 10px 0;

        h4 {
          margin: 0 0 10px 0;
          font-size: 4.13vw;
          white-space: nowrap;       /* 禁止换行 */
          overflow: hidden;          /* 超出部分隐藏 */
          text-overflow: ellipsis;   /* 显示省略号 */
        }

        .item-info {
          font-size: 2.87vw;
          color: #666;

          p {
            margin: 4px 0;
          }
        }
      }

    }

  }

}
</style>
